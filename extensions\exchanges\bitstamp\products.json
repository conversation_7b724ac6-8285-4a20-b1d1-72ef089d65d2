[{"id": "BTCUSD", "asset": "BTC", "currency": "USD", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "BTC/USD"}, {"id": "BTCEUR", "asset": "BTC", "currency": "EUR", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "BTC/EUR"}, {"id": "BTCGBP", "asset": "BTC", "currency": "GBP", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "BTC/GBP"}, {"id": "BTCPAX", "asset": "BTC", "currency": "PAX", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "BTC/PAX"}, {"id": "BTCUSDC", "asset": "BTC", "currency": "USDC", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "BTC/USDC"}, {"id": "GBPUSD", "asset": "GBP", "currency": "USD", "min_size": "0.00001", "min_total": "20", "increment": "0.00001", "label": "GBP/USD"}, {"id": "GBPEUR", "asset": "GBP", "currency": "EUR", "min_size": "0.00001", "min_total": "20", "increment": "0.00001", "label": "GBP/EUR"}, {"id": "EURUSD", "asset": "EUR", "currency": "USD", "min_size": "0.00001", "min_total": "20", "increment": "0.00001", "label": "EUR/USD"}, {"id": "ETHUSD", "asset": "ETH", "currency": "USD", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "ETH/USD"}, {"id": "ETHEUR", "asset": "ETH", "currency": "EUR", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "ETH/EUR"}, {"id": "ETHBTC", "asset": "ETH", "currency": "BTC", "min_size": "0.00000001", "min_total": "0.0002", "increment": "0.00000001", "label": "ETH/BTC"}, {"id": "ETHGBP", "asset": "ETH", "currency": "GBP", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "ETH/GBP"}, {"id": "ETHPAX", "asset": "ETH", "currency": "PAX", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "ETH/PAX"}, {"id": "ETHUSDC", "asset": "ETH", "currency": "USDC", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "ETH/USDC"}, {"id": "XRPUSD", "asset": "XRP", "currency": "USD", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "XRP/USD"}, {"id": "XRPEUR", "asset": "XRP", "currency": "EUR", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "XRP/EUR"}, {"id": "XRPBTC", "asset": "XRP", "currency": "BTC", "min_size": "0.00000001", "min_total": "0.0002", "increment": "0.00000001", "label": "XRP/BTC"}, {"id": "XRPGBP", "asset": "XRP", "currency": "GBP", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "XRP/GBP"}, {"id": "XRPPAX", "asset": "XRP", "currency": "PAX", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "XRP/PAX"}, {"id": "UNIUSD", "asset": "UNI", "currency": "USD", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "UNI/USD"}, {"id": "UNIEUR", "asset": "UNI", "currency": "EUR", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "UNI/EUR"}, {"id": "UNIBTC", "asset": "UNI", "currency": "BTC", "min_size": "0.00000001", "min_total": "0.0002", "increment": "0.00000001", "label": "UNI/BTC"}, {"id": "LTCUSD", "asset": "LTC", "currency": "USD", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "LTC/USD"}, {"id": "LTCEUR", "asset": "LTC", "currency": "EUR", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "LTC/EUR"}, {"id": "LTCBTC", "asset": "LTC", "currency": "BTC", "min_size": "0.00000001", "min_total": "0.0002", "increment": "0.00000001", "label": "LTC/BTC"}, {"id": "LTCGBP", "asset": "LTC", "currency": "GBP", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "LTC/GBP"}, {"id": "LINKUSD", "asset": "LINK", "currency": "USD", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "LINK/USD"}, {"id": "LINKEUR", "asset": "LINK", "currency": "EUR", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "LINK/EUR"}, {"id": "LINKGBP", "asset": "LINK", "currency": "GBP", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "LINK/GBP"}, {"id": "LINKBTC", "asset": "LINK", "currency": "BTC", "min_size": "0.00000001", "min_total": "0.0002", "increment": "0.00000001", "label": "LINK/BTC"}, {"id": "LINKETH", "asset": "LINK", "currency": "ETH", "min_size": "0.00000001", "min_total": "0.005", "increment": "0.00000001", "label": "LINK/ETH"}, {"id": "XLMBTC", "asset": "XLM", "currency": "BTC", "min_size": "0.00000001", "min_total": "0.0002", "increment": "0.00000001", "label": "XLM/BTC"}, {"id": "XLMUSD", "asset": "XLM", "currency": "USD", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "XLM/USD"}, {"id": "XLMEUR", "asset": "XLM", "currency": "EUR", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "XLM/EUR"}, {"id": "XLMGBP", "asset": "XLM", "currency": "GBP", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "XLM/GBP"}, {"id": "BCHUSD", "asset": "BCH", "currency": "USD", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "BCH/USD"}, {"id": "BCHEUR", "asset": "BCH", "currency": "EUR", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "BCH/EUR"}, {"id": "BCHBTC", "asset": "BCH", "currency": "BTC", "min_size": "0.00000001", "min_total": "0.0002", "increment": "0.00000001", "label": "BCH/BTC"}, {"id": "BCHGBP", "asset": "BCH", "currency": "GBP", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "BCH/GBP"}, {"id": "AAVEUSD", "asset": "AAVE", "currency": "USD", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "AAVE/USD"}, {"id": "AAVEEUR", "asset": "AAVE", "currency": "EUR", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "AAVE/EUR"}, {"id": "AAVEBTC", "asset": "AAVE", "currency": "BTC", "min_size": "0.00000001", "min_total": "0.0002", "increment": "0.00000001", "label": "AAVE/BTC"}, {"id": "ALGOUSD", "asset": "ALGO", "currency": "USD", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "ALGO/USD"}, {"id": "ALGOEUR", "asset": "ALGO", "currency": "EUR", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "ALGO/EUR"}, {"id": "ALGOBTC", "asset": "ALGO", "currency": "BTC", "min_size": "0.00000001", "min_total": "0.0002", "increment": "0.00000001", "label": "ALGO/BTC"}, {"id": "SNXUSD", "asset": "SNX", "currency": "USD", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "SNX/USD"}, {"id": "SNXEUR", "asset": "SNX", "currency": "EUR", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "SNX/EUR"}, {"id": "SNXBTC", "asset": "SNX", "currency": "BTC", "min_size": "0.00000001", "min_total": "0.0002", "increment": "0.00000001", "label": "SNX/BTC"}, {"id": "BATUSD", "asset": "BAT", "currency": "USD", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "BAT/USD"}, {"id": "BATEUR", "asset": "BAT", "currency": "EUR", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "BAT/EUR"}, {"id": "BATBTC", "asset": "BAT", "currency": "BTC", "min_size": "0.00000001", "min_total": "0.0002", "increment": "0.00000001", "label": "BAT/BTC"}, {"id": "MKRUSD", "asset": "MKR", "currency": "USD", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "MKR/USD"}, {"id": "MKREUR", "asset": "MKR", "currency": "EUR", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "MKR/EUR"}, {"id": "MKRBTC", "asset": "MKR", "currency": "BTC", "min_size": "0.00000001", "min_total": "0.0002", "increment": "0.00000001", "label": "MKR/BTC"}, {"id": "ZRXUSD", "asset": "ZRX", "currency": "USD", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "ZRX/USD"}, {"id": "ZRXEUR", "asset": "ZRX", "currency": "EUR", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "ZRX/EUR"}, {"id": "ZRXBTC", "asset": "ZRX", "currency": "BTC", "min_size": "0.00000001", "min_total": "0.0002", "increment": "0.00000001", "label": "ZRX/BTC"}, {"id": "YFIUSD", "asset": "YFI", "currency": "USD", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "YFI/USD"}, {"id": "YFIEUR", "asset": "YFI", "currency": "EUR", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "YFI/EUR"}, {"id": "YFIBTC", "asset": "YFI", "currency": "BTC", "min_size": "0.00000001", "min_total": "0.0002", "increment": "0.00000001", "label": "YFI/BTC"}, {"id": "UMAUSD", "asset": "UMA", "currency": "USD", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "UMA/USD"}, {"id": "UMAEUR", "asset": "UMA", "currency": "EUR", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "UMA/EUR"}, {"id": "UMABTC", "asset": "UMA", "currency": "BTC", "min_size": "0.00000001", "min_total": "0.0002", "increment": "0.00000001", "label": "UMA/BTC"}, {"id": "OMGUSD", "asset": "OMG", "currency": "USD", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "OMG/USD"}, {"id": "OMGEUR", "asset": "OMG", "currency": "EUR", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "OMG/EUR"}, {"id": "OMGGBP", "asset": "OMG", "currency": "GBP", "min_size": "0.00000001", "min_total": "20", "increment": "0.01", "label": "OMG/GBP"}, {"id": "OMGBTC", "asset": "OMG", "currency": "BTC", "min_size": "0.00000001", "min_total": "0.0002", "increment": "0.00000001", "label": "OMG/BTC"}, {"id": "KNCUSD", "asset": "KNC", "currency": "USD", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "KNC/USD"}, {"id": "KNCEUR", "asset": "KNC", "currency": "EUR", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "KNC/EUR"}, {"id": "KNCBTC", "asset": "KNC", "currency": "BTC", "min_size": "0.00000001", "min_total": "0.0002", "increment": "0.00000001", "label": "KNC/BTC"}, {"id": "CRVUSD", "asset": "CRV", "currency": "USD", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "CRV/USD"}, {"id": "CRVEUR", "asset": "CRV", "currency": "EUR", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "CRV/EUR"}, {"id": "CRVBTC", "asset": "CRV", "currency": "BTC", "min_size": "0.00000001", "min_total": "0.0002", "increment": "0.00000001", "label": "CRV/BTC"}, {"id": "AUDIOUSD", "asset": "AUDIO", "currency": "USD", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "AUDIO/USD"}, {"id": "AUDIOEUR", "asset": "AUDIO", "currency": "EUR", "min_size": "0.00000001", "min_total": "20", "increment": "0.00001", "label": "AUDIO/EUR"}, {"id": "AUDIOBTC", "asset": "AUDIO", "currency": "BTC", "min_size": "0.00000001", "min_total": "0.0002", "increment": "0.00000001", "label": "AUDIO/BTC"}, {"id": "USDCUSD", "asset": "USDC", "currency": "USD", "min_size": "0.00001", "min_total": "20", "increment": "0.00001", "label": "USDC/USD"}, {"id": "USDCEUR", "asset": "USDC", "currency": "EUR", "min_size": "0.00001", "min_total": "20", "increment": "0.00001", "label": "USDC/EUR"}, {"id": "DAIUSD", "asset": "DAI", "currency": "USD", "min_size": "0.00001", "min_total": "20", "increment": "0.00001", "label": "DAI/USD"}, {"id": "PAXUSD", "asset": "PAX", "currency": "USD", "min_size": "0.00001", "min_total": "20", "increment": "0.00001", "label": "PAX/USD"}, {"id": "PAXEUR", "asset": "PAX", "currency": "EUR", "min_size": "0.00001", "min_total": "20", "increment": "0.00001", "label": "PAX/EUR"}, {"id": "PAXGBP", "asset": "PAX", "currency": "GBP", "min_size": "0.00001", "min_total": "20", "increment": "0.00001", "label": "PAX/GBP"}, {"id": "ETH2ETH", "asset": "ETH2", "currency": "ETH", "min_size": "0.00000001", "min_total": "0.005", "increment": "0.00000001", "label": "ETH2/ETH"}, {"id": "GUSDUSD", "asset": "GUSD", "currency": "USD", "min_size": "0.00001", "min_total": "20", "increment": "0.00001", "label": "GUSD/USD"}]