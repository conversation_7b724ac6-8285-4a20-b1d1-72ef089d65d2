server:
  image: deviavir/zenbot:unstable
  volumes:
    - ./conf.js:/app/conf.js
    - ./extensions:/app/extensions
  links:
    - mongodb
  command: [ "trade", "--paper" ]
  restart: always

mongodb:
  image: mongo:latest
  volumes_from:
    - mongodb-data
  command: mongod --smallfiles --bind_ip=0.0.0.0 --logpath=/dev/null

mongodb-data:
  image: mongo:latest
  volumes:
    - /data/db
  command: "true"
