{"name": "quick-lru", "version": "4.0.1", "description": "Simple \"Least Recently Used\" (LRU) cache", "license": "MIT", "repository": "sindresorhus/quick-lru", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["lru", "quick", "cache", "caching", "least", "recently", "used", "fast", "map", "hash", "buffer"], "devDependencies": {"ava": "^1.4.1", "coveralls": "^3.0.3", "nyc": "^14.1.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}