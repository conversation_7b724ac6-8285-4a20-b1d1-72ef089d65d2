<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="robots" content="noindex, nofollow">
    <link rel="icon" type="image/png" sizes="16x16" href="assets-zenbot/zenbot_square.png">

    <link rel="manifest" href="assets/manifest.json">

    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#ff6600">
    <meta name="msapplication-navbutton-color" content="#ff6600">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="msapplication-starturl" content="/">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <link rel="icon" type="image/png" sizes="330x203" href="assets-zenbot/logo.png">
    <link rel="apple-touch-icon" type="image/png" sizes="330x203" href="assets-zenbot/logo.png">

    <title><%= asset.toUpperCase() %>/<%= currency.toUpperCase() %> on <%= exchange.name.toUpperCase() %> - Dashboard</title>
    <!-- Webpack compiled -->
    <link href="assets-wp/app.bundle.css" rel="stylesheet">
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
    <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>

<body class="hide-sidebar" >
<!-- ============================================================== -->
<!-- Preloader -->
<!-- ============================================================== -->
<div class="preloader">
    <svg class="circular" viewBox="25 25 50 50">
        <circle class="path" cx="50" cy="50" r="20" fill="none" stroke-width="2" stroke-miterlimit="10" />
    </svg>
</div>
<!-- ============================================================== -->
<!-- Wrapper -->
<!-- ============================================================== -->
<div id="wrapper">
    <!-- ============================================================== -->
    <!-- Page Content -->
    <!-- ============================================================== -->
    <div id="page-wrapper">
        <div class="container-fluid">
            <div class="row bg-title">
                <div class="col-lg-3 col-md-4 col-sm-4 col-12">
                    <h4 class="page-title">Dashboard</h4>
                </div>
                <div class="col-lg-9 col-sm-8 col-md-8 col-12">
                    <ol class="breadcrumb">
                        <li>Running for <%= moment(boot_time).toNow(true) %></li>
                    </ol>
                </div>
                <!-- /.col-lg-12 -->
            </div>
            <!-- /.row -->
            <!-- ============================================================== -->
            <!-- Different data widgets -->
            <!-- ============================================================== -->
            <!-- .row -->
            <div class="row">
                <div class="col-lg-6 col-sm-6 col-12">
                    <div class="white-box analytics-info">
                        <h3 class="box-title">Current Market</h3>
                        <div class="row">
                            <div class="col-lg-4 col-12">
                                <ul class="list-inline two-part">
                                    <li class="text-left" style="display:inline"><i class="ti-arrow-up"></i> <span class="text-info">Price</span></li>
                                    <li class="text-left" style="display:inline"><i class="ti-arrow-up text-info"></i> <span><%= new Intl.NumberFormat("en-US", {useGrouping: false, minimumFractionDigits: 2, maximumFractionDigits: 7}).format(period.close) %></span> <small><%= asset.toUpperCase() %></small></li>
                                </ul>
                            </div>
                            <% if (typeof quote != "undefined") { %>
                            <div class="col-lg-4 col-12">
                                <ul class="list-inline two-part">
                                    <li class="text-left" style="display:inline"><i class="ti-arrow-up"></i> <span class="text-success">ASK</span></li>
                                    <li class="text-left" style="display:inline"><i class="ti-arrow-up text-success"></i> <span><%= new Intl.NumberFormat("en-US", {useGrouping: false, minimumFractionDigits: 2, maximumFractionDigits: 7}).format(quote.ask) %></span> <small><%= asset.toUpperCase() %></small></li>
                                </ul>
                            </div>
                            <div class="col-lg-4 col-12">
                                <ul class="list-inline two-part">
                                    <li class="text-left" style="display:inline"><i class="ti-arrow-up"></i> <span class="text-danger">BID</span></li>
                                    <li class="text-left" style="display:inline"><i class="ti-arrow-up text-danger"></i> <span><%= new Intl.NumberFormat("en-US", {useGrouping: false, minimumFractionDigits: 2, maximumFractionDigits: 7}).format(quote.bid) %></span> <small><%= asset.toUpperCase() %></small></li>
                                </ul>
                            </div>
                            <% } %>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-sm-6 col-12">
                    <div class="white-box analytics-info">
                        <h3 class="box-title">Capital</h3>
                        <div class="row">
                            <div class="col-lg-<% if (typeof deposit != 'undefined') { %>4<% } else { %>6<% } %> col-12">
                                <ul class="list-inline two-part">
                                    <li class="text-left" style="display:inline"><i class="ti-arrow-up text-purple"></i> <span class="text-purple">Asset</span></li>
                                    <li class="text-left" style="display:inline"><i class="ti-arrow-up text-purple"></i> <span><%= new Intl.NumberFormat("en-US", {useGrouping: false,  minimumFractionDigits: 2, maximumFractionDigits: 8}).format(balance.asset) %></span> <small><%= asset.toUpperCase() %></small></li>
                                </ul>
                            </div>
                            <% if (typeof deposit != "undefined") { %>
                            <div class="col-lg-4 col-12">
                                <ul class="list-inline two-part">
                                    <li class="text-left" style="display:inline"><i class="ti-arrow-up text-purple"></i> <span class="text-purple">Deposit</span></li>
                                    <li class="text-left" style="display:inline"><i class="ti-arrow-up text-purple"></i> <span><%= new Intl.NumberFormat("en-US", {useGrouping: false,  minimumFractionDigits: 2, maximumFractionDigits: 8}).format(balance.deposit) %></span> <small><%= currency.toUpperCase() %></small></li>
                                </ul>
                            </div>
                            <% } %>
                            <div class="col-lg-<% if (typeof deposit != 'undefined') { %>4<% } else { %>6<% } %> col-12">
                                <ul class="list-inline two-part">
                                    <li class="text-left" style="display:inline"><i class="ti-arrow-up text-purple"></i> <span class="text-purple">Currency</span></li>
                                    <li class="text-left" style="display:inline"><i class="ti-arrow-up text-purple"></i> <span><%= new Intl.NumberFormat("en-US", {useGrouping: false, minimumFractionDigits: 2, maximumFractionDigits: 8}).format(balance.currency) %></span> <small><%= currency.toUpperCase() %></small></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <% if (typeof stats != "undefined") { %>
                <div class="col-lg-3 col-sm-6 col-12">
                    <div class="white-box analytics-info">
                        <h3 class="box-title">Last balance (<%= new Intl.NumberFormat("en-US", {style: "percent", minimumFractionDigits: 2, maximumFractionDigits: 2}).format((parseFloat(stats.profit)/100)-(parseFloat(stats.buy_hold_profit)/100) ) %> vs buy hold)</h3>
                        <ul class="list-inline one-part">
                            <li class="text-right text-success"><i class="ti-arrow-up"></i> <span class="counter"><%= new Intl.NumberFormat("en-US", {useGrouping: false, minimumFractionDigits: 8, maximumFractionDigits: 8}).format(stats.tmp_balance) %></span> <small><%= currency.toUpperCase() %></small></li>
                            <li class="text-right text-<% if (parseFloat(stats.profit) < 0) { %>danger<% } else { %>success<% } %>""><i class="ti-arrow-up"></i> <span><%= stats.profit %></span> <small>of profit</small></li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-3 col-sm-6 col-12">
                    <div class="white-box analytics-info">
                        <h3 class="box-title">Buy hold</h3>
                        <ul class="list-inline one-part">
                            <li class="text-right text-info"><i class="ti-arrow-up"></i> <span class="counter"><%= new Intl.NumberFormat("en-US", {useGrouping: false, minimumFractionDigits: 8, maximumFractionDigits: 8}).format(stats.buy_hold) %></span> <small><%= currency.toUpperCase() %></small></li>
                            <li class="text-right text-<% if (parseFloat(stats.buy_hold_profit) < 0) { %>danger<% } else { %>success<% } %>""><i class="ti-arrow-up"></i> <span><%= stats.buy_hold_profit %></span> <small>of profit</small></li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-3 col-sm-6 col-12">
                    <div class="white-box analytics-info">
                        <h3 class="box-title">Trades per day</h3>
                        <ul class="list-inline one-part">
                            <li class="text-right"><i class="ti-arrow-up text-purple"></i> <span class="counter text-purple"><%= stats.trade_per_day %></span></li>
                            <li class="text-right"><i class="ti-arrow-up text-purple"></i> <span class="text-purple"><%= my_trades.length %> trade<% if (my_trades.length != 1) { %>s<% } %> over <%= day_count %> day<% if (day_count != 1) { %>s<% } %></span></li>
                        </ul>
                    </div>
                </div>
                    <% if (typeof stats.error_rate != "undefined") { %>
                    <div class="col-lg-3 col-sm-6 col-12">
                        <div class="white-box analytics-info">
                            <h3 class="box-title">Error Rate</h3>
                            <ul class="list-inline one-part">
                                <li class="text-right text-<% if (parseFloat(stats.error_rate) > 0) { %>danger<% } else { %>success<% } %>""><i class="ti-arrow-up"></i> <span><%= stats.error_rate %></span></li>
                                <li class="text-right"><i class="ti-arrow-up text-purple"></i> <span class="counter text-purple">Win/Loss: <%= stats.win %>/<%= stats.losses %></span></li>
                            </ul>
                        </div>
                    </div>
                    <% } %>
                <% } %>
            </div>

            <!--/.row -->
            <!--row -->
            <!-- /.row -->
            <div class="row">
                <div class="col-md-12 col-lg-12 col-sm-12 col-12">
                    <div class="white-box">
                        <h3 class="box-title"><%= exchange.name.toUpperCase() %> <%= asset.toUpperCase() %>/<%= currency.toUpperCase() %> Trade chart</h3>
                        <div id="trade_chart" style="height: 505px;">
                        </div>
                        <script src="assets-wp/echarts.bundle.js" charset="utf8"></script>
                        <script type="text/javascript">

                            function unpack(rows, key, offset) {
                                return rows.map(function(row) {
                                    return { value: [ row.time - offset * 60000, row[key] ] };
                                });
                            }

                            var xmlhttp = new XMLHttpRequest();
                            xmlhttp.open("GET", "trades", true);
                            xmlhttp.onreadystatechange = function() {
                                if (this.readyState == 4 && this.status == 200) {
                                    var tradeData = JSON.parse(this.responseText);
                                    var trades = tradeData.trades;
                                    var lookback = tradeData.lookback;
                                    var my_trades = tradeData.my_trades;
                                    var offset = tradeData.tz_offset;

                                    var trade_chart = echarts.init(document.getElementById('trade_chart'));

                                    var lastTrade = trades[trades.length < 500 ? 0 : trades.length - 500].time;
                                    var rangeStart = lastTrade - offset * 60000;

                                    var options = {
                                        useUTC: true,
                                        grid: {
                                            left: 60,
                                            right: 60
                                        },
                                        xAxis: [
                                            {
                                                type: 'time',
                                                axisLabel: {
                                                    formatter: function (value) {
                                                        return echarts.format.formatTime('yyyy-MM-dd hh:mm:ss', value, true);
                                                    }
                                                }
                                            }
                                        ],
                                        yAxis: [
                                            {
                                                name: 'Price',
                                                position: 'right',
                                                scale: true
                                            },
                                            {
                                                name: 'Volume',
                                                scale: true
                                            }
                                        ],
                                        toolbox: {
                                            show: true,
                                            showTitle: false,
                                            feature: {
                                                dataZoom: {},
                                                restore: {},
                                                saveAsImage: {}
                                            }
                                        },
                                        tooltip : {
                                            trigger: 'axis',
                                            axisPointer: {
                                                type: 'cross',
                                            }
                                        },
                                        dataZoom: [
                                            {
                                                startValue: rangeStart
                                            },
                                            {
                                                type: 'inside'
                                            },
                                        ],
                                        series: [
                                            {
                                                name: 'Price',
                                                type: 'line',
                                                data: unpack(trades, 'price', offset)
                                            },
                                            {
                                                name: 'Volume',
                                                type: 'bar',
                                                yAxisIndex: 1,
                                                z: 1,
                                                itemStyle: {
                                                    opacity: 0.25
                                                },
                                                data: unpack(trades, 'size', offset)
                                            },
                                            {
                                                name: 'High',
                                                type: 'candlestick',
                                                z: 1,
                                                itemStyle: {
                                                    color: 'rgba(40,167,69,0.5)',  // bullish
                                                    borderColor: '#28a745',
                                                    color0: 'rgba(220,53,69,0.5)', // bearish
                                                    borderColor0: '#dc3545'
                                                },
                                                data: []
                                            },
                                            {
                                                type: 'line',
                                                smooth: true,
                                                lineStyle: {
                                                    type: 'dotted'
                                                },
                                                data: unpack(my_trades, 'price', offset),
                                                tooltip: {
                                                    show: false
                                                }
                                            },
                                            {
                                                name: 'Buy',
                                                type: 'scatter',
                                                symbol: 'triangle',
                                                data: [],
                                                itemStyle: {
                                                    color: '#28a745'
                                                },
                                                markPoint: {
                                                    symbol: 'arrow',
                                                    itemStyle: {
                                                        color: '#28a745'
                                                    },
                                                    symbolOffset: [ 0, 10 ],
                                                    data: []
                                                }
                                            },
                                            {
                                                name: 'Sell',
                                                type: 'scatter',
                                                symbol: 'triangle',
                                                symbolRotate: 180,
                                                data: [],
                                                itemStyle: {
                                                    color: '#dc3545'
                                                },
                                                markPoint: {
                                                    symbol: 'arrow',
                                                    itemStyle: {
                                                        color: '#dc3545'
                                                    },
                                                    symbolRotate: 180,
                                                    symbolOffset: [ 0, -10 ],
                                                    data: []
                                                }
                                            }
                                        ]
                                    };

                                    for (i = 0; i < lookback.length; i++) {
                                        options.series[2].data.push({
                                            value: [ lookback[i].close_time - offset * 60000, lookback[i].open, lookback[i].close, lookback[i].low, lookback[i].high ] // OCLH -> OHLC
                                        });
                                    }

                                    for (i = 0; i < my_trades.length; i++) {
                                        if (my_trades[i].type == 'buy') {
                                            options.series[4].data.push({
                                                value: [ my_trades[i].time - offset * 60000, my_trades[i].price ]
                                            });
                                            options.series[4].markPoint.data.push({
                                                xAxis: my_trades[i].time - offset * 60000,
                                                yAxis: my_trades[i].price,
                                                value: 'Bought',
                                            });
                                        } else {
                                            options.series[5].data.push({
                                                value: [ my_trades[i].time - offset * 60000, my_trades[i].price ]
                                            });
                                            options.series[5].markPoint.data.push({
                                                xAxis: my_trades[i].time - offset * 60000,
                                                yAxis: my_trades[i].price,
                                                value: 'Sold',
                                            });
                                        }
                                    }

                                    trade_chart.setOption(options);

                                    window.onresize = function() {
                                        trade_chart.resize();
                                    };
                                }
                            };
                            xmlhttp.send();
                        </script>
                    </div>
                </div>
            </div>
            <!-- ============================================================== -->
            <!-- table -->
            <!-- ============================================================== -->
            <div class="row">
                <div class="col-md-12 col-lg-12 col-sm-12">
                    <div class="white-box">
                        <h3 class="box-title">My trades</h3>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                <tr>
                                    <th>TYPE</th>
                                    <th>AMOUNT</th>
                                    <th>PRICE</th>
                                    <th>TOTAL</th>
                                    <th>FEE</th>
                                    <th>SLIPPAGE</th>
                                    <th>DATE</th>
                                    <th>EXECUTION TIME</th>
                                    <th>PROFIT</th>
                                </tr>
                                </thead>
                                <tbody>
                                <% if (typeof buy_order != "undefined") { %>
                                <tr>
                                    <td><span class='text-success'>BUY</span></td>
                                    <td><%= new Intl.NumberFormat("en-US", {useGrouping: false, minimumFractionDigits: 8, maximumFractionDigits: 8}).format(buy_order.size) %> <%= asset.toUpperCase() %></td>
                                    <td><%= new Intl.NumberFormat("en-US", {useGrouping: false, minimumFractionDigits: 2, maximumFractionDigits: 7}).format(buy_order.price) %> <%= currency.toUpperCase() %></td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td><%= moment(buy_order.time).format('YYYY-MM-DD HH:mm:ss') %></td>
                                    <td><span class='text-info'>Waiting</span></td>
                                    <td>-</td>
                                </tr>
                                <% } %>
                                <% if (typeof sell_order != "undefined") { %>
                                <tr>
                                    <td><span class='text-danger'>SELL</span></td>
                                    <td><%= new Intl.NumberFormat("en-US", {useGrouping: false, minimumFractionDigits: 8, maximumFractionDigits: 8}).format(sell_order.size) %> <%= asset.toUpperCase() %></td>
                                    <td><%= new Intl.NumberFormat("en-US", {useGrouping: false, minimumFractionDigits: 2, maximumFractionDigits: 7}).format(sell_order.price) %> <%= currency.toUpperCase() %></td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td>-</td>
                                    <td><%= moment(sell_order.time).format('YYYY-MM-DD HH:mm:ss') %></td>
                                    <td><span class='text-info'>Waiting</span></td>
                                    <td>-</td>
                                </tr>
                                <% } %>
                                <% if (my_trades) { %>
                                <% my_trades.sort(function(a,b){return a.time > b.time ? -1 : 1;}).forEach(function(trade){ %>
                                <tr>
                                    <td><span class='text-<% if (trade.type == "buy") { %>success<% } else { %>danger<% } %>'><%= trade.type.toUpperCase() %></span></td>
                                    <td><%= new Intl.NumberFormat("en-US", {useGrouping: false, minimumFractionDigits: 8, maximumFractionDigits: 8}).format(trade.size) %> <%= asset.toUpperCase() %></td>
                                    <td><%= new Intl.NumberFormat("en-US", {useGrouping: false, minimumFractionDigits: 2, maximumFractionDigits: 7}).format(trade.price) %> <%= currency.toUpperCase() %></td>
                                    <td><%= new Intl.NumberFormat("en-US", {useGrouping: false, minimumFractionDigits: 2, maximumFractionDigits: 7}).format(trade.size * trade.price) %> <%= currency.toUpperCase() %></td>
                                    <td><%= new Intl.NumberFormat("en-US", {useGrouping: false, minimumFractionDigits: 2, maximumFractionDigits: 8}).format(trade.fee) %> <% if (trade.type == "buy") { %> <%= asset.toUpperCase() %> <% } else { %> <%= currency.toUpperCase() %> <% } %></td>
                                    <td><span class="text-<% if (trade.slippage > 0) { %>danger<% } else { %>success<% } %>"><%= new Intl.NumberFormat("en-US", {style: "percent", useGrouping: false, minimumFractionDigits: 4, maximumFractionDigits: 4}).format(trade.slippage) %></span></td>
                                    <td><%= moment(trade.time).format('YYYY-MM-DD HH:mm:ss') %></td>
                                    <td><%= moment.duration(trade.execution_time).humanize() %></td>
                                    <td><% if (trade.profit != null) { %><span class="text-<% if(trade.profit < 0) { %>danger<% } else { %>success<% } %>"><%= new Intl.NumberFormat("en-US", {style: "percent", useGrouping: false, minimumFractionDigits: 2, maximumFractionDigits: 2}).format(trade.profit) %></span><% } else { %>-<% } %></td>
                                </tr>
                                <% }); %>
                                <% } %>
                                <% if (my_prev_trades) { %>
                                <% my_prev_trades.reverse().forEach(function(trade){ %>
                                <tr class="text-muted">
                                    <td><%= trade.type.toUpperCase() %></span></td>
                                    <td><%= new Intl.NumberFormat("en-US", {useGrouping: false, minimumFractionDigits: 8, maximumFractionDigits: 8}).format(trade.size) %> <%= asset.toUpperCase() %></td>
                                    <td><%= new Intl.NumberFormat("en-US", {useGrouping: false, minimumFractionDigits: 2, maximumFractionDigits: 7}).format(trade.price) %> <%= currency.toUpperCase() %></td>
                                    <td><%= new Intl.NumberFormat("en-US", {useGrouping: false, minimumFractionDigits: 2, maximumFractionDigits: 7}).format(trade.size * trade.price) %> <%= currency.toUpperCase() %></td>
                                    <td><%= new Intl.NumberFormat("en-US", {useGrouping: false, minimumFractionDigits: 2, maximumFractionDigits: 8}).format(trade.fee) %> <% if (trade.type == "buy") { %> <%= asset.toUpperCase() %> <% } else { %> <%= currency.toUpperCase() %> <% } %></td>
                                    <td><%= new Intl.NumberFormat("en-US", {style: "percent", useGrouping: false, minimumFractionDigits: 4, maximumFractionDigits: 4}).format(trade.slippage) %></td>
                                    <td><%= moment(trade.time).format('YYYY-MM-DD HH:mm:ss') %></td>
                                    <td><%= moment.duration(trade.execution_time).humanize() %></td>
                                    <td><% if (trade.profit) { %><%= new Intl.NumberFormat("en-US", {style: "percent", useGrouping: false, minimumFractionDigits: 2, maximumFractionDigits: 2}).format(trade.profit) %><% } else { %>-<% } %></td>
                                </tr>
                                <% }); %>
                                <% } %>

                                <% if (!(typeof buy_order != "undefined" || typeof sell_order != "undefined") && !my_trades) { %>
                                <tr><td colspan="6" class="text-center">There is no trades at the moment</td></tr>
                                <% } %>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>


            <!-- ============================================================== -->
            <!-- table -->
            <!-- ============================================================== -->
            <div class="row">
                <div class="col-md-12 col-lg-12 col-sm-12">
                    <div class="white-box">
                        <h3 class="box-title">CoinMarketCap Data</h3>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                <tr>
                                    <th class='text-center'>RANK</th>
                                    <th align="left">NAME</th>
                                    <th class='text-right'>PRICE (USD)</th>
                                    <th class='text-right hidden-xs'>PRICE (BTC)</th>
                                    <th class='text-right hidden-xs hidden-sm'>VOLUME</th>
                                    <th class='text-right hidden-xs hidden-sm'>MARKET CAP</th>
                                    <th class='text-right hidden-xs hidden-sm'>CURRENT SUPPLY</th>
                                    <th class='text-right'>CHANGE 1H</th>
                                    <th class='text-right'>CHANGE 24H</th>
                                    <th class='text-right'>CHANGE 7D</th>
                                </tr>
                                </thead>
                                <tbody id="market_data">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /.container-fluid -->
        <footer class="footer text-center"> <%= (new Date()).getFullYear() %> &copy; zenbot </footer>
    </div>
    <!-- ============================================================== -->
    <!-- End Page Content -->
    <!-- ============================================================== -->
</div>
<!-- ============================================================== -->
<!-- End Wrapper -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- All Jquery -->
<!-- ============================================================== -->
<!-- Webpack compiled -->
<script src="assets-wp/app.bundle.js"></script>
<!--<script src="assets-wp/common.bundle.js"></script>-->

<script>

    var xmlhttp = new XMLHttpRequest();
    xmlhttp.onreadystatechange = function() {
        if (this.readyState == 4 && this.status == 200) {
            var myObj = JSON.parse(this.responseText);

            var row = "";
            for (s = 0; s < myObj.length; s++) {
                if(s==25)
                    break;

                row += "<tr>";
                row += "<td class='text-center'>" + myObj[s].rank + "</td>";
                row += "<td><div class='s-s-"+myObj[s].id+" currency-logo-sprite'></div>" + myObj[s].name + " ("+myObj[s].symbol+")" +  "</td>";
                row += "<td class='text-right'>" + new Intl.NumberFormat("en-US", {style: "currency", currency: "USD", minimumSignificantDigits: 2, maximumSignificantDigits: 6}).format(myObj[s].price_usd) + "</td>";
                row += "<td class='text-right hidden-xs'>" + new Intl.NumberFormat("en-US", {minimumSignificantDigits: 2, maximumSignificantDigits: 6}).format(myObj[s].price_btc) + " BTC</td>";
                row += "<td class='text-right hidden-xs hidden-sm'>" + new Intl.NumberFormat("en-US", {style: "currency", currency: "USD"}).format(myObj[s]["24h_volume_usd"]) + "</td>";
                row += "<td class='text-right hidden-xs hidden-sm'>" + new Intl.NumberFormat("en-US", {style: "currency", currency: "USD"}).format(myObj[s].market_cap_usd) + "</td>";
                row += "<td class='text-right hidden-xs hidden-sm'>" + new Intl.NumberFormat("en-US").format(myObj[s].total_supply) + " " + myObj[s].symbol + "</td>";
                row += "<td class='text-right'><span class='text-"+((myObj[s].percent_change_1h > 0) ? 'success' : 'danger' )+"'>" + new Intl.NumberFormat("en-US", {style: "percent", minimumSignificantDigits: 2}).format(myObj[s].percent_change_1h/100) + "</span></td>";
                row += "<td class='text-right'><span class='text-"+((myObj[s].percent_change_24h > 0) ? 'success' : 'danger' )+"'>" + new Intl.NumberFormat("en-US", {style: "percent", minimumSignificantDigits: 2}).format(myObj[s].percent_change_24h/100) + "</span></td>";
                row += "<td class='text-right'><span class='text-"+((myObj[s].percent_change_7d > 0) ? 'success' : 'danger' )+"'>" + new Intl.NumberFormat("en-US", {style: "percent", minimumSignificantDigits: 2}).format(myObj[s].percent_change_7d/100) + "</span></td>";
                row += "</tr>";
            }


            document.getElementById("market_data").innerHTML = row;
        }

    };


    xmlhttp.open("GET", "https://api.coinmarketcap.com/v1/ticker/", true);
    xmlhttp.send();
</script>
</body>

</html>
