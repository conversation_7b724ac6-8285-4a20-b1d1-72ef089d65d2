## Notifiers

<PERSON><PERSON> employs various notifiers to keep you up to date on the bot's actions. We currently send a notification on a buy and on a sell signal.

### XMPP

Supply Zenbot with your XMPP credentials and <PERSON><PERSON> will send notifications by connecting to your XMPP, sending the notification, and disconnecting.
https://xmpp.org/

### Pushbullet

Supply Zenbot with your API key and device ID and <PERSON>bot will send notifications to your device.
https://www.pushbullet.com/

### IFTTT

Supply Zenbot with your IFTTT maker key and <PERSON><PERSON> will push notifications to your IFTTT.
https://ifttt.com/maker_webhooks

### Slack

Supply Zenbot with a webhook URI and Zenbot will push notifications to your webhook.
https://slack.com/

### Discord

Supply Zenbot with your Discord webhook id and webhook token and <PERSON>bot will push notifications to your Discord channel.

How to add a webhook to a Discord channel
https://support.discordapp.com/hc/en-us/articles/228383668

### Prowl

Supply Zenbot with your Prowl API key and <PERSON><PERSON> will push notifications to your Prowl enabled devices.
https://www.prowlapp.com/

### Textbelt

Supply Zenbot with your Textbelt API key and <PERSON><PERSON> will send SMS notifications to your cell phone.
https://www.textbelt.com/

### Pushover

Supply Zenbot with your api token and user key and Zenbot will send notifications to your device.
https://pushover.net/

### Telegram

Supply Zenbot with your Telegram bot token and chat id and Zenbot will push notifications to your Telegram chat.
https://telegram.org/

### ADAMANT Messenger

Supply Zenbot with recipients' ADM addresses, sender's account passPhrase and node list and Zenbot will push notifications to ADAMANT chats.
https://adamant.im/
