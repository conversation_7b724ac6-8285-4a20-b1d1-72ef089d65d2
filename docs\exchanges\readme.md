# Zenbot Exchange API Tips
Since Zenbot supports a number of exchanges, it's becoming harder to provide "default" polling settings that work well with all exchange APIs. The goal of this document is to share any settings or tweaks that we find to increase reliability. Anything from proventing API rate limit lockouts (GDAX) to ensuring orders are tracked properly (Kraken).

Any contribution that makes this better for everyone is certainly welcome.

## Exchanges

* [Bitstamp](bitstamp.md)
* [GDAX](gdax.md)
* [Kraken](kraken.md)


