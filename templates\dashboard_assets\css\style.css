@import url(https://fonts.googleapis.com/css?family=Rubik:300,400,500,700,900);
@import url(https://coinmarketcap.com/static/public/sprites/all_views_market-cap-by-circulating-supply_0.css);

@import "spinners.css";
.preloader {
    width: 100%;
    height: 100%;
    top: 0;
    position: fixed;
    z-index: 99999;
    background: #fff
}
.preloader .cssload-speeding-wheel {
    position: absolute;
    top: calc(50% - 3.5px);
    left: calc(50% - 3.5px)
}
* {
    outline: 0!important
}
body {
    background: #2f323e;
    font-family: Rubik, sans-serif;
    margin: 0;
    overflow-x: hidden;
    color: #313131;
    font-weight: 300
}
html {
    position: relative;
    min-height: 100%;
    background: #fff
}
h1,
h2,
h3,
h4,
h5,
h6 {
    color: #313131;
    font-family: Rubik, sans-serif;
    margin: 10px 0;
    font-weight: 300
}
h1 {
    line-height: 48px;
    font-size: 36px
}
h2 {
    line-height: 36px;
    font-size: 24px
}
h3 {
    line-height: 30px;
    font-size: 21px
}
h4 {
    line-height: 22px;
    font-size: 18px
}
h5 {
    font-size: 16px;
    font-size: 14px
}
.dn {
    display: none
}
.db {
    display: block
}
.light_op_text {
    color: rgba(255, 255, 255, .5)
}
blockquote {
    border-left: 5px solid #2cabe3!important;
    border: 1px solid rgba(120, 130, 140, .13)
}
p {
    line-height: 1.6
}
b {
    font-weight: 500
}
a:active,
a:focus,
a:hover {
    outline: 0;
    text-decoration: none
}
.clear {
    clear: both
}
.font-12 {
    font-size: 12px
}
hr {
    border-color: rgba(120, 130, 140, .13)
}
.b-t {
    border-top: 1px solid rgba(120, 130, 140, .13)
}
.b-b {
    border-bottom: 1px solid rgba(120, 130, 140, .13)
}
.b-l {
    border-left: 1px solid rgba(120, 130, 140, .13)
}
.b-r {
    border-right: 1px solid rgba(120, 130, 140, .13)
}
.b-all {
    border: 1px solid rgba(120, 130, 140, .13)
}
.b-none {
    border: 0!important
}
.max-height {
    height: 310px;
    overflow: auto
}
.p-0 {
    padding: 0!important
}
.p-10 {
    padding: 10px!important
}
.p-20 {
    padding: 20px!important
}
.p-30 {
    padding: 30px!important
}
.p-l-0 {
    padding-left: 0!important
}
.p-l-10 {
    padding-left: 10px!important
}
.p-l-20 {
    padding-left: 20px!important
}
.p-l-30 {
    padding-left: 30px!important
}
.p-r-0 {
    padding-right: 0!important
}
.p-r-10 {
    padding-right: 10px!important
}
.p-r-20 {
    padding-right: 20px!important
}
.p-r-30 {
    padding-right: 30px!important
}
.p-r-40 {
    padding-right: 40px!important
}
.p-t-0 {
    padding-top: 0!important
}
.p-t-10 {
    padding-top: 10px!important
}
.p-t-20 {
    padding-top: 20px!important
}
.p-t-30 {
    padding-top: 30px!important
}
.p-b-0 {
    padding-bottom: 0!important
}
.p-b-10 {
    padding-bottom: 10px!important
}
.p-b-20 {
    padding-bottom: 20px!important
}
.p-b-30 {
    padding-bottom: 30px!important
}
.p-b-40 {
    padding-bottom: 40px!important
}
.m-0 {
    margin: 0!important
}
.m-l-5 {
    margin-left: 5px!important
}
.m-l-10 {
    margin-left: 10px!important
}
.m-l-15 {
    margin-left: 15px!important
}
.m-l-20 {
    margin-left: 20px!important
}
.m-l-30 {
    margin-left: 30px!important
}
.m-l-40 {
    margin-left: 40px!important
}
.m-r-5 {
    margin-right: 5px!important
}
.m-r-10 {
    margin-right: 10px!important
}
.m-r-15 {
    margin-right: 15px!important
}
.m-r-20 {
    margin-right: 20px!important
}
.m-r-30 {
    margin-right: 30px!important
}
.m-r-40 {
    margin-right: 40px!important
}
.m-t-5 {
    margin-top: 5px!important
}
.m-t-0 {
    margin-top: 0!important
}
.m-t-10 {
    margin-top: 10px!important
}
.m-t-15 {
    margin-top: 15px!important
}
.m-t-20 {
    margin-top: 20px!important
}
.m-t-30 {
    margin-top: 30px!important
}
.m-t-40 {
    margin-top: 40px!important
}
.m-b-0 {
    margin-bottom: 0!important
}
.m-b-5 {
    margin-bottom: 5px!important
}
.m-b-10 {
    margin-bottom: 10px!important
}
.m-b-15 {
    margin-bottom: 15px!important
}
.m-b-20 {
    margin-bottom: 20px!important
}
.m-b-30 {
    margin-bottom: 30px!important
}
.m-b-40 {
    margin-bottom: 40px!important
}
.vt {
    vertical-align: top
}
.vb {
    vertical-align: bottom
}
.font-bold {
    font-weight: 700
}
.font-medium {
    font-weight: 500
}
.font-normal {
    font-weight: 400
}
.font-light {
    font-weight: 300
}
.pull-in {
    margin-left: -15px;
    margin-right: -15px
}
.b-0 {
    border: none!important
}
.vertical-middle,
.vm {
    vertical-align: middle
}
.mdi {
    font-size: 17px
}
.bx-shadow {
    -moz-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .1);
    -webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .1);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .1)
}
.mx-box {
    max-height: 380px;
    min-height: 380px
}
.thumb-sm {
    height: 32px;
    width: 32px
}
.thumb-md {
    height: 48px;
    width: 48px
}
.thumb-lg {
    height: 88px;
    width: 88px
}
.txt-oflo {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap
}
.di {
    display: inline-block
}
.get-code {
    color: #263238;
    cursor: pointer;
    border-radius: 100%;
    background: #fff;
    padding: 4px 5px;
    font-size: 10px;
    margin: 0 5px;
    vertical-align: middle
}
.badge {
    text-transform: uppercase;
    font-weight: 600;
    padding: 3px 5px;
    font-size: 12px;
    margin-top: 1px;
    background-color: #fb4
}
.badge-xs {
    font-size: 9px
}
.badge-sm,
.badge-xs {
    -webkit-transform: translate(0, -2px);
    -ms-transform: translate(0, -2px);
    -o-transform: translate(0, -2px);
    transform: translate(0, -2px)
}
.badge-success {
    background-color: #7ace4c
}
.badge-info {
    background-color: #41b3f9
}
.badge-warning {
    background-color: #fb4
}
.badge-danger {
    background-color: #f33155
}
.badge-purple {
    background-color: #707cd2
}
.badge-red {
    background-color: #f33155
}
.badge-inverse {
    background-color: #4c5667
}
.notify {
    position: relative;
    margin-top: -30px
}
.notify .heartbit {
    position: absolute;
    top: -20px;
    right: -16px;
    height: 25px;
    width: 25px;
    z-index: 10;
    border: 5px solid #f33155;
    border-radius: 70px;
    -moz-animation: heartbit 1s ease-out;
    -moz-animation-iteration-count: infinite;
    -o-animation: heartbit 1s ease-out;
    -o-animation-iteration-count: infinite;
    -webkit-animation: heartbit 1s ease-out;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite
}
.notify .point {
    width: 6px;
    height: 6px;
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    border-radius: 30px;
    background-color: #f33155;
    position: absolute;
    right: -6px;
    top: -10px
}
@-moz-keyframes heartbit {
    0% {
        -moz-transform: scale(0);
        opacity: 0
    }
    25% {
        -moz-transform: scale(.1);
        opacity: .1
    }
    50% {
        -moz-transform: scale(.5);
        opacity: .3
    }
    75% {
        -moz-transform: scale(.8);
        opacity: .5
    }
    100% {
        -moz-transform: scale(1);
        opacity: 0
    }
}
@-webkit-keyframes heartbit {
    0% {
        -webkit-transform: scale(0);
        opacity: 0
    }
    25% {
        -webkit-transform: scale(.1);
        opacity: .1
    }
    50% {
        -webkit-transform: scale(.5);
        opacity: .3
    }
    75% {
        -webkit-transform: scale(.8);
        opacity: .5
    }
    100% {
        -webkit-transform: scale(1);
        opacity: 0
    }
}
.text-white {
    color: #fff
}
.text-danger {
    color: #f33155
}
.text-muted {
    color: #8d9ea7
}
.text-warning {
    color: #fb4
}
.text-success {
    color: #7ace4c
}
.text-info {
    color: #41b3f9
}
.text-inverse {
    color: #4c5667
}
.text-blue {
    color: #02bec9
}
.text-purple {
    color: #707cd2
}
.text-primary {
    color: #7460ee
}
.text-megna {
    color: #01c0c8
}
.text-dark {
    color: #313131!important
}
.fw-500 {
    font-weight: 500
}
.bg-primary {
    background-color: #7460ee!important
}
.bg-success {
    background-color: #7ace4c!important
}
.bg-info {
    background-color: #41b3f9!important
}
.bg-warning {
    background-color: #fb4!important
}
.bg-danger,
.bg-theme-alt {
    background-color: #f33155!important
}
.bg-theme {
    background-color: #2cabe3!important
}
.bg-theme-dark {
    background-color: #4f5467!important
}
.bg-inverse {
    background-color: #4c5667!important
}
.bg-purple {
    background-color: #707cd2!important
}
.bg-white {
    background-color: #fff!important
}
.bg-light {
    background-color: #e4e7ea!important
}
.bg-extralight {
    background-color: #f7fafc!important
}
.label {
    letter-spacing: .05em;
    border-radius: 60px;
    padding: 4px 12px 3px;
    font-weight: 500
}
.label-rouded,
.label-rounded {
    border-radius: 60px;
    padding: 4px 12px 3px;
    font-weight: 500
}
.label-custom {
    background-color: #01c0c8
}
.label-success {
    background-color: #7ace4c
}
.label-info {
    background-color: #41b3f9
}
.label-warning {
    background-color: #fb4
}
.label-danger {
    background-color: #f33155
}
.label-megna {
    background-color: #01c0c8
}
.label-primary {
    background-color: #7460ee
}
.label-purple {
    background-color: #707cd2
}
.label-red {
    background-color: #f33155
}
.label-inverse {
    background-color: #4c5667
}
.label-white {
    background-color: #fff
}
.label-default {
    background-color: #e4e7ea
}
.dropdown-menu {
    border: 1px solid rgba(120, 130, 140, .13);
    border-radius: 0;
    box-shadow: 0 3px 12px rgba(0, 0, 0, .05)!important;
    -webkit-box-shadow: 0!important;
    -moz-box-shadow: 0!important;
    padding-bottom: 8px;
    margin-top: 0
}
.dropdown-menu>li>a {
    padding: 9px 20px
}
.dropdown-menu>li>a:focus,
.dropdown-menu>li>a:hover {
    background: #f7fafc
}
.navbar-top-links .progress {
    margin-bottom: 6px
}
label {
    font-weight: 500
}
.btn {
    border-radius: 3px
}
.form-control {
    background-color: #fff;
    border: 1px solid #e4e7ea;
    border-radius: 0;
    box-shadow: 4px;
    color: #565656;
    height: 38px;
    max-width: 100%;
    padding: 7px 12px;
    transition: all 300ms linear 0s
}
.form-control:focus {
    box-shadow: none;
    border-color: #263238
}
.input-sm {
    height: 30px;
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5
}
.input-lg {
    height: 44px;
    padding: 5px 10px;
    font-size: 18px
}
.bootstrap-tagsinput {
    border: 1px solid #e4e7ea;
    border-radius: 0;
    box-shadow: none;
    display: block;
    padding: 7px 12px
}
.bootstrap-touchspin .input-group-btn-vertical>.btn {
    padding: 9px 10px
}
.bootstrap-touchspin .input-group-btn-vertical .bootstrap-touchspin-down,
.bootstrap-touchspin .input-group-btn-vertical .bootstrap-touchspin-up {
    border-radius: 0
}
.input-group-btn .btn {
    padding: 8px 12px
}
.form-group,
.form-horizontal .form-group {
    margin-bottom: 25px
}
.list-group-item,
.list-group-item:first-child,
.list-group-item:last-child {
    border-radius: 0;
    border-color: rgba(120, 130, 140, .13)
}
.list-group-item.active,
.list-group-item.active:focus,
.list-group-item.active:hover {
    background: #41b3f9;
    border-color: #41b3f9
}
.list-task .list-group-item,
.list-task .list-group-item:first-child {
    border-radius: 0;
    border: 0
}
.list-task .list-group-item:last-child {
    border-radius: 0;
    border: 0
}
.media {
    border: 1px solid rgba(120, 130, 140, .13);
    margin-bottom: 10px;
    padding: 15px
}
.media .media-heading {
    font-weight: 500
}
.well,
pre {
    background: #fff;
    border-radius: 0
}
.nav-tabs>li>a {
    border-radius: 0;
    color: #263238
}
.nav-tabs>li>a:focus,
.nav-tabs>li>a:hover {
    background: #fff
}
.modal-content {
    border-radius: 0;
    box-shadow: 0 5px 15px rgba(0, 0, 0, .1)
}
.alert {
    border-radius: 0
}
.carousel-control {
    width: 8%
}
.carousel-control span {
    position: absolute;
    top: 50%;
    z-index: 5;
    display: inline-block;
    font-size: 30px
}
.popover {
    border-radius: 0;
    z-index: 100
}
.popover-title {
    padding: 5px 14px
}
.container-fluid {
    padding-left: 25px;
    padding-right: 25px;
    padding-bottom: 15px
}
.btn-group-vertical>.btn:first-child:not(:last-child),
.btn-group-vertical>.btn:last-child:not(:first-child) {
    border-radius: 0
}
.table-responsive {
    overflow-y: hidden
}
.pagination>li:first-child>a,
.pagination>li:first-child>span {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}
.pagination>li:last-child>a,
.pagination>li:last-child>span {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}
.pagination>li>a,
.pagination>li>span {
    color: #263238
}
.pagination>li>a:focus,
.pagination>li>a:hover,
.pagination>li>span:focus,
.pagination>li>span:hover {
    background-color: #e4e7ea
}
.pagination-split li {
    margin-left: 5px;
    display: inline-block;
    float: left
}
.pagination-split li:first-child {
    margin-left: 0
}
.pagination-split li a {
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    border-radius: 0
}
.pagination>.active>a,
.pagination>.active>a:focus,
.pagination>.active>a:hover,
.pagination>.active>span,
.pagination>.active>span:focus,
.pagination>.active>span:hover {
    background-color: #2cabe3;
    border-color: #2cabe3
}
.pager li>a,
.pager li>span {
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    border-radius: 0;
    color: #263238
}
.table-box {
    display: table;
    width: 100%
}
.cell {
    display: table-cell;
    vertical-align: middle
}
.jqstooltip {
    width: auto!important;
    height: auto!important
}
#wrapper {
    width: 100%
}
#page-wrapper {
    padding: 0 0 60px;
    min-height: 568px;
    background: #edf1f5
}
.footer {
    bottom: 0;
    color: #58666e;
    left: 240px;
    padding: 20px 30px;
    position: absolute;
    right: 0;
    background: #fff
}
.bg-title {
    background: #fff;
    overflow: hidden;
    padding: 15px 10px 9px;
    margin-bottom: 25px;
    margin-left: -25.5px;
    margin-right: -25.5px
}
.bg-title h4 {
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 500;
    margin-top: 6px
}
.bg-title .breadcrumb {
    background: 0 0;
    margin-bottom: 0;
    float: right;
    padding: 0;
    margin-top: 8px
}
.bg-title .breadcrumb a {
    color: rgba(0, 0, 0, .5)
}
.bg-title .breadcrumb a:hover {
    color: #000
}
.bg-title .breadcrumb .active {
    color: #2cabe3
}
.logo b {
    height: 60px;
    float: left;
    padding-left: 10px;
    width: auto;
    line-height: 59px;
    text-align: center
}
.logo i {
    color: #fff
}
.top-left-part {
    width: 240px;
    float: left;
    border-right: 1px solid rgba(0, 0, 0, .08)
}
.top-left-part a {
    color: #fff;
    line-height: 59px;
    font-size: 18px;
    padding-left: 10px;
    text-transform: uppercase
}
.top-left-part .light-logo {
    display: none
}
.navbar-header {
    width: 100%;
    background: #3c4451;
    border: 0
}
.navbar-default {
    border: 0
}
.navbar-top-links {
    margin-right: 0
}
.navbar-top-links .badge {
    position: absolute;
    right: 6px;
    top: 15px
}
.navbar-top-links>li {
    float: left
}
.navbar-top-links>li>a {
    color: #fff;
    padding: 0 14px;
    line-height: 60px;
    min-height: 60px
}
.navbar-top-links>li>a:hover {
    background: rgba(0, 0, 0, .1)
}
.navbar-top-links>li>a:focus {
    background: rgba(0, 0, 0, 0)
}
.nav .open>a,
.nav .open>a:focus,
.nav .open>a:hover {
    background: rgba(255, 255, 255, .2)
}
.navbar-top-links .dropdown-menu li {
    display: block
}
.navbar-top-links .dropdown-menu li:last-child {
    margin-right: 0
}
.navbar-top-links .dropdown-menu li a div {
    white-space: normal
}
.navbar-top-links .dropdown-alerts,
.navbar-top-links .dropdown-messages,
.navbar-top-links .dropdown-tasks {
    width: 310px;
    min-width: 0
}
.navbar-top-links .dropdown-user {
    right: 0;
    left: auto;
    width: 280px
}
.navbar-top-links .dropdown-user .dw-user-box {
    padding: 15px
}
.navbar-top-links .dropdown-user .dw-user-box .u-img {
    width: 80px;
    display: inline-block;
    vertical-align: top
}
.navbar-top-links .dropdown-user .dw-user-box .u-img img {
    width: 100%;
    border-radius: 5px
}
.navbar-top-links .dropdown-user .dw-user-box .u-text {
    display: inline-block;
    padding-left: 10px
}
.navbar-top-links .dropdown-user .dw-user-box .u-text h4 {
    margin: 0
}
.navbar-top-links .dropdown-user .dw-user-box .u-text p {
    margin-bottom: 3px
}
.navbar-header .navbar-toggle {
    float: none;
    padding: 0 15px;
    line-height: 60px;
    border: 0;
    color: rgba(255, 255, 255, .5);
    margin: 0;
    display: inline-block;
    border-radius: 0
}
.navbar-header .navbar-toggle:focus,
.navbar-header .navbar-toggle:hover {
    background: rgba(0, 0, 0, .3);
    color: #fff
}
.app-search {
    position: relative;
    margin: 0
}
.app-search a {
    position: absolute;
    top: 20px;
    right: 10px;
    color: #4c5667
}
.app-search .form-control,
.app-search .form-control:focus {
    border: none;
    font-size: 13px;
    color: #4c5667;
    padding-left: 20px;
    padding-right: 40px;
    background: rgba(255, 255, 255, .9);
    box-shadow: none;
    height: 30px;
    font-weight: 600;
    width: 180px;
    display: inline-block;
    line-height: 30px;
    margin-top: 15px;
    border-radius: 40px;
    transition: .5s ease-out
}
.app-search .form-control::-moz-placeholder {
    color: #4c5667;
    opacity: .5
}
.app-search .form-control::-webkit-input-placeholder {
    color: #4c5667;
    opacity: .5
}
.app-search .form-control::-ms-placeholder {
    color: #4c5667;
    opacity: .5
}
.nav-small-cap {
    color: #a6afbb;
    cursor: default;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 13px;
    letter-spacing: .035em;
    padding: 12px 15px!important;
    pointer-events: none;
    margin: 20px 0 0 -15px
}
.profile-pic {
    padding: 0 20px;
    line-height: 50px
}
.profile-pic img {
    margin-right: 10px
}
.drop-title {
    border-bottom: 1px solid rgba(0, 0, 0, .1);
    color: #263238;
    font-size: 15px;
    font-weight: 600;
    padding: 11px 20px 15px
}
.btn-outline {
    color: inherit;
    background-color: transparent;
    transition: all .5s
}
.btn-rounded {
    border-radius: 60px
}
.btn-custom,
.btn-custom.disabled {
    background: #2cabe3;
    border: 1px solid #2cabe3;
    color: #fff
}
.btn-custom.disabled.focus,
.btn-custom.disabled:focus,
.btn-custom.disabled:hover,
.btn-custom.focus,
.btn-custom:focus,
.btn-custom:hover {
    background: #2cabe3;
    opacity: .8;
    color: #fff;
    border: 1px solid #2cabe3
}
.btn-primary,
.btn-primary.disabled {
    background: #7460ee;
    border: 1px solid #7460ee
}
.btn-primary.disabled.focus,
.btn-primary.disabled:focus,
.btn-primary.disabled:hover,
.btn-primary.focus,
.btn-primary:focus,
.btn-primary:hover {
    background: #7460ee;
    opacity: .8;
    border: 1px solid #7460ee
}
.btn-success,
.btn-success.disabled {
    background: #7ace4c;
    border: 1px solid #7ace4c
}
.btn-success.disabled.focus,
.btn-success.disabled:focus,
.btn-success.disabled:hover,
.btn-success.focus,
.btn-success:focus,
.btn-success:hover {
    background: #7ace4c;
    opacity: .8;
    border: 1px solid #7ace4c
}
.btn-info,
.btn-info.disabled {
    background: #41b3f9;
    border: 1px solid #41b3f9
}
.btn-info.disabled.focus,
.btn-info.disabled:focus,
.btn-info.disabled:hover,
.btn-info.focus,
.btn-info:focus,
.btn-info:hover {
    background: #41b3f9;
    opacity: .8;
    border: 1px solid #41b3f9
}
.btn-warning,
.btn-warning.disabled {
    background: #fb4;
    border: 1px solid #fb4
}
.btn-warning.disabled.focus,
.btn-warning.disabled:focus,
.btn-warning.disabled:hover,
.btn-warning.focus,
.btn-warning:focus,
.btn-warning:hover {
    background: #fb4;
    opacity: .8;
    border: 1px solid #fb4
}
.btn-danger,
.btn-danger.disabled {
    background: #f33155;
    border: 1px solid #f33155
}
.btn-danger.disabled.focus,
.btn-danger.disabled:focus,
.btn-danger.disabled:hover,
.btn-danger.focus,
.btn-danger:focus,
.btn-danger:hover {
    background: #f33155;
    opacity: .8;
    border: 1px solid #f33155
}
.btn-default,
.btn-default.disabled {
    background: #e4e7ea;
    border: 1px solid #e4e7ea
}
.btn-default.disabled.focus,
.btn-default.disabled:focus,
.btn-default.disabled:hover,
.btn-default.focus,
.btn-default:focus,
.btn-default:hover {
    opacity: .8;
    border: 1px solid #e4e7ea;
    background: #e4e7ea
}
.btn-default.btn-outline {
    background-color: #fff
}
.btn-default.btn-outline.focus,
.btn-default.btn-outline:focus,
.btn-default.btn-outline:hover {
    background: #e4e7ea
}
.btn-primary.btn-outline {
    color: #7460ee;
    background-color: #fff
}
.btn-primary.btn-outline.focus,
.btn-primary.btn-outline:focus,
.btn-primary.btn-outline:hover {
    background: #7460ee;
    color: #fff
}
.btn-success.btn-outline {
    color: #7ace4c;
    background-color: transparent
}
.btn-success.btn-outline.focus,
.btn-success.btn-outline:focus,
.btn-success.btn-outline:hover {
    background: #7ace4c;
    color: #fff
}
.btn-info.btn-outline {
    color: #41b3f9;
    background-color: transparent
}
.btn-info.btn-outline.focus,
.btn-info.btn-outline:focus,
.btn-info.btn-outline:hover {
    background: #41b3f9;
    color: #fff
}
.btn-warning.btn-outline {
    color: #fb4;
    background-color: transparent
}
.btn-warning.btn-outline.focus,
.btn-warning.btn-outline:focus,
.btn-warning.btn-outline:hover {
    background: #fb4;
    color: #fff
}
.btn-danger.btn-outline {
    color: #f33155;
    background-color: transparent
}
.btn-danger.btn-outline.focus,
.btn-danger.btn-outline:focus,
.btn-danger.btn-outline:hover {
    background: #f33155;
    color: #fff
}
.button-box .btn {
    margin: 0 8px 8px 0
}
.btn-danger.btn-outline:hover,
.btn-info.btn-outline:hover,
.btn-primary.btn-outline:hover,
.btn-success.btn-outline:hover,
.btn-warning.btn-outline:hover {
    color: #fff
}
.btn-label {
    background: rgba(0, 0, 0, .05);
    display: inline-block;
    margin: -6px 12px -6px -14px;
    padding: 7px 15px
}
.btn-facebook {
    color: #fff!important;
    background-color: #3b5998!important
}
.btn-twitter {
    color: #fff!important;
    background-color: #55acee!important
}
.btn-linkedin {
    color: #fff!important;
    background-color: #007bb6!important
}
.btn-dribbble {
    color: #fff!important;
    background-color: #ea4c89!important
}
.btn-googleplus {
    color: #fff!important;
    background-color: #dd4b39!important
}
.btn-instagram {
    color: #fff!important;
    background-color: #3f729b!important
}
.btn-pinterest {
    color: #fff!important;
    background-color: #cb2027!important
}
.btn-dropbox {
    color: #fff!important;
    background-color: #007ee5!important
}
.btn-flickr {
    color: #fff!important;
    background-color: #ff0084!important
}
.btn-tumblr {
    color: #fff!important;
    background-color: #32506d!important
}
.btn-skype {
    color: #fff!important;
    background-color: #00aff0!important
}
.btn-youtube {
    color: #fff!important;
    background-color: #b00!important
}
.btn-github {
    color: #fff!important;
    background-color: #171515!important
}
.btn-primary.active.focus,
.btn-primary.active:focus,
.btn-primary.active:hover,
.btn-primary.focus,
.btn-primary.focus:active,
.btn-primary:active:focus,
.btn-primary:active:hover,
.btn-primary:focus,
.open>.dropdown-toggle.btn-primary.focus,
.open>.dropdown-toggle.btn-primary:focus,
.open>.dropdown-toggle.btn-primary:hover {
    background-color: #7460ee;
    border: 1px solid #7460ee
}
.btn-success.active.focus,
.btn-success.active:focus,
.btn-success.active:hover,
.btn-success.focus,
.btn-success.focus:active,
.btn-success:active:focus,
.btn-success:active:hover,
.btn-success:focus,
.open>.dropdown-toggle.btn-success.focus,
.open>.dropdown-toggle.btn-success:focus,
.open>.dropdown-toggle.btn-success:hover {
    background-color: #7ace4c;
    border: 1px solid #7ace4c
}
.btn-info.active.focus,
.btn-info.active:focus,
.btn-info.active:hover,
.btn-info.focus,
.btn-info.focus:active,
.btn-info:active:focus,
.btn-info:active:hover,
.btn-info:focus,
.open>.dropdown-toggle.btn-info.focus,
.open>.dropdown-toggle.btn-info:focus,
.open>.dropdown-toggle.btn-info:hover {
    background-color: #41b3f9;
    border: 1px solid #41b3f9
}
.btn-warning.active.focus,
.btn-warning.active:focus,
.btn-warning.active:hover,
.btn-warning.focus,
.btn-warning.focus:active,
.btn-warning:active:focus,
.btn-warning:active:hover,
.btn-warning:focus,
.open>.dropdown-toggle.btn-warning.focus,
.open>.dropdown-toggle.btn-warning:focus,
.open>.dropdown-toggle.btn-warning:hover {
    background-color: #fb4;
    border: 1px solid #fb4
}
.btn-danger.active.focus,
.btn-danger.active:focus,
.btn-danger.active:hover,
.btn-danger.focus,
.btn-danger.focus:active,
.btn-danger:active:focus,
.btn-danger:active:hover,
.btn-danger:focus,
.open>.dropdown-toggle.btn-danger.focus,
.open>.dropdown-toggle.btn-danger:focus,
.open>.dropdown-toggle.btn-danger:hover {
    background-color: #f33155;
    border: 1px solid #f33155
}
.btn-inverse,
.btn-inverse.active,
.btn-inverse.focus,
.btn-inverse:active,
.btn-inverse:focus,
.btn-inverse:hover,
.open>.dropdown-toggle.btn-inverse {
    background-color: #4c5667;
    border: 1px solid #4c5667;
    color: #fff
}
.chat {
    margin: 0;
    padding: 0;
    list-style: none
}
.chat li {
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px dotted rgba(120, 130, 140, .13)
}
.chat li.left .chat-body {
    margin-left: 60px
}
.chat li.right .chat-body {
    margin-right: 60px
}
.chat li .chat-body p {
    margin: 0
}
.chat .glyphicon,
.panel .slidedown .glyphicon {
    margin-right: 5px
}
.chat-panel .panel-body {
    height: 350px;
    overflow-y: scroll
}
.login-panel {
    margin-top: 25%
}
.flot-chart {
    display: block;
    height: 400px
}
.flot-chart-content {
    width: 100%;
    height: 100%
}
table.dataTable thead .sorting,
table.dataTable thead .sorting_asc,
table.dataTable thead .sorting_asc_disabled,
table.dataTable thead .sorting_desc,
table.dataTable thead .sorting_desc_disabled {
    background: 0 0
}
table.dataTable thead .sorting_asc:after {
    content: "\f0de";
    float: right;
    font-family: fontawesome
}
table.dataTable thead .sorting_desc:after {
    content: "\f0dd";
    float: right;
    font-family: fontawesome
}
table.dataTable thead .sorting:after {
    content: "\f0dc";
    float: right;
    font-family: fontawesome;
    color: rgba(50, 50, 50, .5)
}
.btn-circle {
    width: 30px;
    height: 30px;
    padding: 6px 0;
    border-radius: 15px;
    text-align: center;
    font-size: 12px;
    line-height: 1.428571429
}
.btn-circle.btn-lg {
    width: 50px;
    height: 50px;
    padding: 10px 16px;
    border-radius: 25px;
    font-size: 18px;
    line-height: 1.33
}
.btn-circle.btn-xl {
    width: 70px;
    height: 70px;
    padding: 10px 16px;
    border-radius: 35px;
    font-size: 24px;
    line-height: 1.33
}
.show-grid [class^=col-] {
    padding-top: 10px;
    padding-bottom: 10px;
    border: 1px solid rgba(120, 130, 140, .13);
    background-color: #f7fafc
}
.show-grid {
    margin: 15px 0
}
.huge {
    font-size: 40px
}
.white-box {
    background: #fff;
    padding: 25px;
    margin-bottom: 30px
}
.white-box .box-title {
    margin: 0 0 12px;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 16px
}
.panel {
    border-radius: 0;
    margin-bottom: 30px;
    border: 0;
    box-shadow: none
}
.panel .panel-heading {
    border-radius: 0;
    font-weight: 500;
    font-size: 16px;
    padding: 20px 25px
}
.panel .panel-heading .panel-title {
    font-size: 16px;
    color: #263238
}
.panel .panel-heading a i {
    font-size: 12px;
    margin-left: 8px
}
.panel .panel-action {
    float: right
}
.panel .panel-action a {
    opacity: .5
}
.panel .panel-action a:hover {
    opacity: 1
}
.panel .panel-body {
    padding: 25px
}
.panel .panel-body:first-child h3 {
    margin-top: 0;
    font-weight: 500;
    font-family: Rubik, sans-serif;
    font-size: 14px;
    text-transform: uppercase
}
.panel .panel-footer {
    background: #fff;
    border-radius: 0;
    padding: 20px 25px
}
.panel-green,
.panel-success {
    border-color: #7ace4c
}
.panel-green .panel-heading,
.panel-success .panel-heading {
    border-color: #7ace4c;
    color: #fff;
    background-color: #7ace4c
}
.panel-green .panel-heading a,
.panel-success .panel-heading a {
    color: #fff
}
.panel-green .panel-heading a:hover,
.panel-success .panel-heading a:hover {
    color: rgba(255, 255, 255, .5)
}
.panel-green a,
.panel-success a {
    color: #7ace4c
}
.panel-green a:hover,
.panel-success a:hover {
    color: #56a12c
}
.panel-black,
.panel-inverse {
    border-color: #4c5667
}
.panel-black .panel-heading,
.panel-inverse .panel-heading {
    border-color: #4c5667;
    color: #fff;
    background-color: #4c5667
}
.panel-black .panel-heading a,
.panel-inverse .panel-heading a {
    color: #fff
}
.panel-black .panel-heading a:hover,
.panel-inverse .panel-heading a:hover {
    color: rgba(255, 255, 255, .5)
}
.panel-black a,
.panel-inverse a {
    color: #4c5667
}
.panel-black a:hover,
.panel-inverse a:hover {
    color: #2c313b
}
.panel-darkblue,
.panel-primary {
    border-color: #7460ee
}
.panel-darkblue .panel-heading,
.panel-primary .panel-heading {
    border-color: #7460ee;
    color: #fff;
    background-color: #7460ee
}
.panel-darkblue .panel-heading a,
.panel-primary .panel-heading a {
    color: #fff
}
.panel-darkblue .panel-heading a:hover,
.panel-primary .panel-heading a:hover {
    color: rgba(255, 255, 255, .5)
}
.panel-darkblue a,
.panel-primary a {
    color: #7460ee
}
.panel-darkblue a:hover,
.panel-primary a:hover {
    color: #381be7
}
.panel-blue,
.panel-info {
    border-color: #41b3f9
}
.panel-blue .panel-heading,
.panel-info .panel-heading {
    border-color: #41b3f9;
    color: #fff;
    background-color: #41b3f9
}
.panel-blue .panel-heading a,
.panel-info .panel-heading a {
    color: #fff
}
.panel-blue .panel-heading a:hover,
.panel-info .panel-heading a:hover {
    color: rgba(255, 255, 255, .5)
}
.panel-blue a,
.panel-info a {
    color: #41b3f9
}
.panel-blue a:hover,
.panel-info a:hover {
    color: #0791e6
}
.panel-danger,
.panel-red {
    border-color: #f33155
}
.panel-danger .panel-heading,
.panel-red .panel-heading {
    border-color: #f33155;
    color: #fff;
    background-color: #f33155
}
.panel-danger .panel-heading a,
.panel-red .panel-heading a {
    color: #fff
}
.panel-danger .panel-heading a:hover,
.panel-red .panel-heading a:hover {
    color: rgba(255, 255, 255, .5)
}
.panel-danger a,
.panel-red a {
    color: #f33155
}
.panel-danger a:hover,
.panel-red a:hover {
    color: #cc0c2f
}
.panel-warning,
.panel-yellow {
    border-color: #fb4
}
.panel-warning .panel-heading,
.panel-yellow .panel-heading {
    border-color: #fb4;
    color: #fff;
    background-color: #fb4
}
.panel-warning .panel-heading a,
.panel-yellow .panel-heading a {
    color: #fff
}
.panel-warning .panel-heading a:hover,
.panel-yellow .panel-heading a:hover {
    color: rgba(255, 255, 255, .5)
}
.panel-warning a,
.panel-yellow a {
    color: #fb4
}
.panel-warning a:hover,
.panel-yellow a:hover {
    color: #f69d00
}
.panel-theme,
.panel-themecolor {
    border-color: #2cabe3
}
.panel-theme .panel-heading,
.panel-themecolor .panel-heading {
    border-color: #2cabe3;
    color: #fff;
    background-color: #2cabe3
}
.panel-theme .panel-heading a,
.panel-themecolor .panel-heading a {
    color: #fff
}
.panel-theme .panel-heading a:hover,
.panel-themecolor .panel-heading a:hover {
    color: rgba(255, 255, 255, .5)
}
.panel-theme a,
.panel-themecolor a {
    color: #2cabe3
}
.panel-theme a:hover,
.panel-themecolor a:hover {
    color: #177eac
}
.panel-default,
.panel-white {
    border-color: rgba(120, 130, 140, .13)
}
.panel-default .panel-heading,
.panel-white .panel-heading {
    color: #263238;
    background-color: #fff;
    border-bottom: 1px solid rgba(120, 130, 140, .13)
}
.panel-default .panel-body,
.panel-white .panel-body {
    color: #263238
}
.panel-default .panel-action a,
.panel-white .panel-action a {
    color: #263238;
    opacity: .5
}
.panel-default .panel-action a:hover,
.panel-white .panel-action a:hover {
    opacity: 1;
    color: #263238
}
.panel-default .panel-footer,
.panel-white .panel-footer {
    background: #fff;
    color: #263238;
    border-top: 1px solid rgba(120, 130, 140, .13)
}
.full-panel-info {
    border-color: #41b3f9
}
.full-panel-info .panel-heading {
    border-color: #41b3f9;
    color: #fff;
    background-color: #41b3f9;
    border-bottom: 1px solid rgba(120, 130, 140, .13)
}
.full-panel-info .panel-body {
    background: #41b3f9;
    color: #fff
}
.full-panel-info .panel-footer {
    background: #41b3f9;
    color: #fff;
    border-top: 1px solid rgba(120, 130, 140, .13)
}
.full-panel-info a {
    color: #41b3f9
}
.full-panel-info a:hover {
    color: #0791e6
}
.full-panel-warning {
    border-color: #fb4
}
.full-panel-warning .panel-heading {
    border-color: #fb4;
    color: #fff;
    background-color: #fb4;
    border-bottom: 1px solid rgba(120, 130, 140, .13)
}
.full-panel-warning .panel-body {
    background: #fb4;
    color: #fff
}
.full-panel-warning .panel-footer {
    background: #fb4;
    color: #fff;
    border-top: 1px solid rgba(120, 130, 140, .13)
}
.full-panel-warning a {
    color: #fb4
}
.full-panel-warning a:hover {
    color: #f69d00
}
.full-panel-success {
    border-color: #7ace4c
}
.full-panel-success .panel-heading {
    border-color: #7ace4c;
    color: #fff;
    background-color: #7ace4c;
    border-bottom: 1px solid rgba(120, 130, 140, .13)
}
.full-panel-success .panel-body {
    background: #7ace4c;
    color: #fff
}
.full-panel-success .panel-footer {
    background: #7ace4c;
    color: #fff;
    border-top: 1px solid rgba(120, 130, 140, .13)
}
.full-panel-success a {
    color: #7ace4c
}
.full-panel-success a:hover {
    color: #56a12c
}
.full-panel-purple {
    border-color: #707cd2
}
.full-panel-purple .panel-heading {
    color: #fff;
    background-color: #707cd2;
    border-bottom: 1px solid rgba(120, 130, 140, .13)
}
.full-panel-purple .panel-body {
    background: #707cd2;
    color: #fff
}
.full-panel-purple .panel-footer {
    background: #707cd2;
    color: #fff;
    border-top: 1px solid rgba(120, 130, 140, .13)
}
.full-panel-purple a {
    color: #707cd2
}
.full-panel-purple a:hover {
    color: #3b4abb
}
.full-panel-danger {
    border-color: #f33155
}
.full-panel-danger .panel-heading {
    border-color: #f33155;
    color: #fff;
    background-color: #f33155;
    border-bottom: 1px solid rgba(120, 130, 140, .13)
}
.full-panel-danger .panel-body {
    background: #f33155;
    color: #fff
}
.full-panel-danger .panel-footer {
    background: #f33155;
    color: #fff;
    border-top: 1px solid rgba(120, 130, 140, .13)
}
.full-panel-danger a {
    color: #f33155
}
.full-panel-danger a:hover {
    color: #cc0c2f
}
.full-panel-inverse {
    border-color: #4c5667
}
.full-panel-inverse .panel-heading {
    border-color: #4c5667;
    color: #fff;
    background-color: #4c5667;
    border-bottom: 1px solid rgba(120, 130, 140, .13)
}
.full-panel-inverse .panel-body {
    background: #4c5667;
    color: #fff
}
.full-panel-inverse .panel-footer {
    background: #4c5667;
    color: #fff;
    border-top: 1px solid rgba(120, 130, 140, .13)
}
.full-panel-inverse a {
    color: #4c5667
}
.full-panel-inverse a:hover {
    color: #2c313b
}
.full-panel-default {
    border-color: rgba(120, 130, 140, .13)
}
.full-panel-default .panel-heading {
    color: #263238;
    background-color: #fff;
    border-bottom: 1px solid rgba(120, 130, 140, .13)
}
.full-panel-default .panel-body {
    color: #263238
}
.full-panel-default .panel-footer {
    background: #fff;
    color: #263238;
    border-top: 1px solid rgba(120, 130, 140, .13)
}
.full-panel-default a {
    color: #263238
}
.full-panel-default a:hover {
    color: #2c313b
}
.panel-opcl {
    float: right
}
.panel-opcl i {
    margin-left: 8px;
    font-size: 10px;
    cursor: pointer
}
.fa-fw {
    width: 20px!important;
    display: inline-block!important;
    text-align: left!important
}
.waves-effect {
    position: relative;
    cursor: pointer;
    display: inline-block;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent
}
.waves-effect .waves-ripple {
    position: absolute;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    margin-top: -10px;
    margin-left: -10px;
    opacity: 0;
    background: rgba(0, 0, 0, .08);
    -webkit-transition: all .5s ease-out;
    -moz-transition: all .5s ease-out;
    -o-transition: all .5s ease-out;
    transition: all .5s ease-out;
    -webkit-transition-property: -webkit-transform, opacity;
    -moz-transition-property: -moz-transform, opacity;
    -o-transition-property: -o-transform, opacity;
    transition-property: transform, opacity;
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);
    pointer-events: none
}
.waves-effect.waves-light .waves-ripple {
    background: rgba(255, 255, 255, .4);
    background: -webkit-radial-gradient(rgba(255, 255, 255, .2) 0, rgba(255, 255, 255, .3) 40%, rgba(255, 255, 255, .4) 50%, rgba(255, 255, 255, .5) 60%, rgba(255, 255, 255, 0) 70%);
    background: -o-radial-gradient(rgba(255, 255, 255, .2) 0, rgba(255, 255, 255, .3) 40%, rgba(255, 255, 255, .4) 50%, rgba(255, 255, 255, .5) 60%, rgba(255, 255, 255, 0) 70%);
    background: -moz-radial-gradient(rgba(255, 255, 255, .2) 0, rgba(255, 255, 255, .3) 40%, rgba(255, 255, 255, .4) 50%, rgba(255, 255, 255, .5) 60%, rgba(255, 255, 255, 0) 70%);
    background: radial-gradient(rgba(255, 255, 255, .2) 0, rgba(255, 255, 255, .3) 40%, rgba(255, 255, 255, .4) 50%, rgba(255, 255, 255, .5) 60%, rgba(255, 255, 255, 0) 70%)
}
.waves-effect.waves-classic .waves-ripple {
    background: rgba(0, 0, 0, .2)
}
.waves-effect.waves-classic.waves-light .waves-ripple {
    background: rgba(255, 255, 255, .4)
}
.waves-notransition {
    -webkit-transition: none!important;
    -moz-transition: none!important;
    -o-transition: none!important;
    transition: none!important
}
.waves-button,
.waves-circle {
    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
    -o-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-mask-image: -webkit-radial-gradient(circle, #fff 100%, #000 100%)
}
.waves-button,
.waves-button-input,
.waves-button:hover,
.waves-button:visited {
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    border: none;
    outline: 0;
    color: inherit;
    background-color: rgba(0, 0, 0, 0);
    font-size: 1em;
    line-height: 1em;
    text-align: center;
    text-decoration: none;
    z-index: 1
}
.waves-button {
    padding: .85em 1.1em;
    border-radius: .2em
}
.waves-button-input {
    margin: 0;
    padding: .85em 1.1em
}
.waves-input-wrapper {
    border-radius: .2em;
    vertical-align: bottom
}
.waves-input-wrapper.waves-button {
    padding: 0
}
.waves-input-wrapper .waves-button-input {
    position: relative;
    top: 0;
    left: 0;
    z-index: 1
}
.waves-circle {
    text-align: center;
    width: 2.5em;
    height: 2.5em;
    line-height: 2.5em;
    border-radius: 50%
}
.waves-float {
    -webkit-mask-image: none;
    -webkit-box-shadow: 0 1px 1.5px 1px rgba(0, 0, 0, .12);
    box-shadow: 0 1px 1.5px 1px rgba(0, 0, 0, .12);
    -webkit-transition: all 300ms;
    -moz-transition: all 300ms;
    -o-transition: all 300ms;
    transition: all 300ms
}
.waves-float:active {
    -webkit-box-shadow: 0 8px 20px 1px rgba(0, 0, 0, .3);
    box-shadow: 0 8px 20px 1px rgba(0, 0, 0, .3)
}
.waves-block {
    display: block
}
.common-list {
    margin: 0;
    padding: 0
}
.common-list li {
    list-style: none;
    display: block
}
.common-list li a {
    padding: 12px 0;
    color: #313131;
    display: block
}
.common-list li a:hover {
    color: #2cabe3
}
.checkbox {
    padding-left: 20px
}
.checkbox label {
    display: inline-block;
    padding-left: 5px;
    position: relative
}
.checkbox label::before {
    -o-transition: .3s ease-in-out;
    -webkit-transition: .3s ease-in-out;
    background-color: #fff;
    border-radius: 1px;
    border: 1px solid rgba(120, 130, 140, .13);
    content: "";
    display: inline-block;
    height: 17px;
    left: 0;
    margin-left: -20px;
    position: absolute;
    transition: .3s ease-in-out;
    width: 17px;
    outline: 0!important
}
.checkbox label::after {
    color: #263238;
    display: inline-block;
    font-size: 11px;
    height: 16px;
    left: 0;
    margin-left: -20px;
    padding-left: 3px;
    padding-top: 1px;
    position: absolute;
    top: 0;
    width: 16px
}
.checkbox input[type=checkbox] {
    cursor: pointer;
    opacity: 0;
    z-index: 1;
    outline: 0!important
}
.checkbox input[type=checkbox]:disabled+label {
    opacity: .65
}
.checkbox input[type=checkbox]:focus+label::before {
    outline-offset: -2px;
    outline: 0;
    outline: dotted thin
}
.checkbox input[type=checkbox]:checked+label::after {
    content: "\f00c";
    font-family: FontAwesome
}
.checkbox input[type=checkbox]:disabled+label::before {
    background-color: #e4e7ea;
    cursor: not-allowed
}
.checkbox.checkbox-circle label::before {
    border-radius: 50%
}
.checkbox.checkbox-inline {
    margin-top: 0
}
.checkbox.checkbox-single label {
    height: 17px
}
.checkbox-primary input[type=checkbox]:checked+label::before {
    background-color: #7460ee;
    border-color: #7460ee
}
.checkbox-primary input[type=checkbox]:checked+label::after {
    color: #fff
}
.checkbox-danger input[type=checkbox]:checked+label::before {
    background-color: #f33155;
    border-color: #f33155
}
.checkbox-danger input[type=checkbox]:checked+label::after {
    color: #fff
}
.checkbox-info input[type=checkbox]:checked+label::before {
    background-color: #41b3f9;
    border-color: #41b3f9
}
.checkbox-info input[type=checkbox]:checked+label::after {
    color: #fff
}
.checkbox-warning input[type=checkbox]:checked+label::before {
    background-color: #fb4;
    border-color: #fb4
}
.checkbox-warning input[type=checkbox]:checked+label::after {
    color: #fff
}
.checkbox-success input[type=checkbox]:checked+label::before {
    background-color: #7ace4c;
    border-color: #7ace4c
}
.checkbox-success input[type=checkbox]:checked+label::after {
    color: #fff
}
.checkbox-purple input[type=checkbox]:checked+label::before {
    background-color: #707cd2;
    border-color: #707cd2
}
.checkbox-purple input[type=checkbox]:checked+label::after {
    color: #fff
}
.checkbox-red input[type=checkbox]:checked+label::before {
    background-color: #f33155;
    border-color: #f33155
}
.checkbox-red input[type=checkbox]:checked+label::after {
    color: #fff
}
.checkbox-inverse input[type=checkbox]:checked+label::before {
    background-color: #4c5667;
    border-color: #4c5667
}
.checkbox-inverse input[type=checkbox]:checked+label::after {
    color: #fff
}
.radio {
    padding-left: 20px
}
.radio label {
    display: inline-block;
    padding-left: 5px;
    position: relative
}
.radio label::before {
    -o-transition: border .5s ease-in-out;
    -webkit-transition: border .5s ease-in-out;
    background-color: #fff;
    border-radius: 50%;
    border: 1px solid rgba(120, 130, 140, .13);
    content: "";
    display: inline-block;
    height: 17px;
    left: 0;
    margin-left: -20px;
    position: absolute;
    transition: border .5s ease-in-out;
    width: 17px;
    outline: 0!important
}
.radio label::after {
    -moz-transition: -moz-transform .3s cubic-bezier(.8, -.33, .2, 1.33);
    -ms-transform: scale(0, 0);
    -o-transform: scale(0, 0);
    -o-transition: -o-transform .3s cubic-bezier(.8, -.33, .2, 1.33);
    -webkit-transform: scale(0, 0);
    -webkit-transition: -webkit-transform .3s cubic-bezier(.8, -.33, .2, 1.33);
    background-color: #263238;
    border-radius: 50%;
    content: " ";
    display: inline-block;
    height: 7px;
    left: 5px;
    margin-left: -20px;
    position: absolute;
    top: 5px;
    transform: scale(0, 0);
    transition: transform .3s cubic-bezier(.8, -.33, .2, 1.33);
    width: 7px
}
.radio input[type=radio] {
    cursor: pointer;
    opacity: 0;
    z-index: 1;
    outline: 0!important
}
.radio input[type=radio]:disabled+label {
    opacity: .65
}
.radio input[type=radio]:focus+label::before {
    outline-offset: -2px;
    outline: -webkit-focus-ring-color auto 5px;
    outline: dotted thin
}
.radio input[type=radio]:checked+label::after {
    -ms-transform: scale(1, 1);
    -o-transform: scale(1, 1);
    -webkit-transform: scale(1, 1);
    transform: scale(1, 1)
}
.radio input[type=radio]:disabled+label::before {
    cursor: not-allowed
}
.radio.radio-inline {
    margin-top: 0
}
.radio.radio-single label {
    height: 17px
}
.radio-primary input[type=radio]+label::after {
    background-color: #7460ee
}
.radio-primary input[type=radio]:checked+label::before {
    border-color: #7460ee
}
.radio-primary input[type=radio]:checked+label::after {
    background-color: #7460ee
}
.radio-danger input[type=radio]+label::after {
    background-color: #f33155
}
.radio-danger input[type=radio]:checked+label::before {
    border-color: #f33155
}
.radio-danger input[type=radio]:checked+label::after {
    background-color: #f33155
}
.radio-info input[type=radio]+label::after {
    background-color: #41b3f9
}
.radio-info input[type=radio]:checked+label::before {
    border-color: #41b3f9
}
.radio-info input[type=radio]:checked+label::after {
    background-color: #41b3f9
}
.radio-warning input[type=radio]+label::after {
    background-color: #fb4
}
.radio-warning input[type=radio]:checked+label::before {
    border-color: #fb4
}
.radio-warning input[type=radio]:checked+label::after {
    background-color: #fb4
}
.radio-success input[type=radio]+label::after {
    background-color: #7ace4c
}
.radio-success input[type=radio]:checked+label::before {
    border-color: #7ace4c
}
.radio-success input[type=radio]:checked+label::after {
    background-color: #7ace4c
}
.radio-purple input[type=radio]+label::after {
    background-color: #707cd2
}
.radio-purple input[type=radio]:checked+label::before {
    border-color: #707cd2
}
.radio-purple input[type=radio]:checked+label::after {
    background-color: #707cd2
}
.radio-red input[type=radio]+label::after {
    background-color: #f33155
}
.radio-red input[type=radio]:checked+label::before {
    border-color: #f33155
}
.radio-red input[type=radio]:checked+label::after {
    background-color: #f33155
}
.fileupload {
    overflow: hidden;
    position: relative
}
.fileupload input.upload {
    cursor: pointer;
    filter: alpha(opacity=0);
    font-size: 20px;
    margin: 0;
    opacity: 0;
    padding: 0;
    position: absolute;
    right: 0;
    top: 0
}
.model_img {
    cursor: pointer
}
.myadmin-dd .dd-list .dd-item .dd-handle {
    background: #fff;
    border: 1px solid rgba(120, 130, 140, .13);
    padding: 8px 16px;
    height: auto;
    font-weight: 600;
    border-radius: 0
}
.myadmin-dd .dd-list .dd-item .dd-handle:hover {
    color: #41b3f9
}
.myadmin-dd .dd-list .dd-item button {
    height: auto;
    font-size: 17px;
    margin: 8px auto;
    color: #263238;
    width: 30px
}
.myadmin-dd-empty .dd-list .dd3-handle {
    border: 1px solid rgba(120, 130, 140, .13);
    border-bottom: 0;
    background: #fff;
    height: 36px;
    width: 36px
}
.myadmin-dd-empty .dd-list .dd3-handle:before {
    color: inherit;
    top: 7px
}
.myadmin-dd-empty .dd-list .dd3-handle:hover {
    color: #41b3f9
}
.myadmin-dd-empty .dd-list .dd3-content {
    height: auto;
    border: 1px solid rgba(120, 130, 140, .13);
    padding: 8px 16px 8px 46px;
    background: #fff;
    font-weight: 600
}
.myadmin-dd-empty .dd-list .dd3-content:hover {
    color: #41b3f9
}
.myadmin-dd-empty .dd-list button {
    width: 26px;
    height: 26px;
    font-size: 16px;
    font-weight: 600
}
.settings_box {
    position: absolute;
    top: 75px;
    right: 0;
    z-index: 100
}
.settings_box a {
    background: #fff;
    padding: 15px;
    display: inline-block;
    vertical-align: top
}
.settings_box a i {
    display: block;
    -webkit-animation-name: rotate;
    -webkit-animation-duration: 2s;
    -moz-animation-name: rotate;
    -moz-animation-duration: 2s;
    -moz-animation-iteration-count: infinite;
    -moz-animation-timing-function: linear;
    animation-name: rotate;
    font-size: 16px;
    animation-duration: 1s;
    animation-iteration-count: infinite;
    animation-timing-function: linear
}
@-webkit-keyframes rotate {
    from {
        -webkit-transform: rotate(0deg)
    }
    to {
        -webkit-transform: rotate(360deg)
    }
}
@-moz-keyframes rotate {
    from {
        -moz-transform: rotate(0deg)
    }
    to {
        -moz-transform: rotate(360deg)
    }
}
@keyframes rotate {
    from {
        transform: rotate(0deg)
    }
    to {
        transform: rotate(360deg)
    }
}
.theme_color {
    margin: 0;
    padding: 0;
    display: inline-block;
    overflow: hidden;
    width: 0;
    transition: .5s ease-out;
    background: #fff
}
.theme_color li {
    list-style: none;
    width: 30%;
    float: left;
    margin: 0 1.5%
}
.theme_color li a {
    padding: 5px;
    height: 50px;
    display: block
}
.theme_color li a.theme-green {
    background: #7ace4c
}
.theme_color li a.theme-red {
    background: #f33155
}
.theme_color li a.theme-dark {
    background: #4c5667
}
.theme_block {
    width: 200px;
    padding: 30px
}
ul.common li {
    display: inline-block;
    line-height: 40px;
    list-style: none none;
    width: 48%
}
ul.common li a {
    color: #313131
}
ul.common li a:hover {
    color: #41b3f9
}
.circle {
    border-radius: 100%;
    text-align: center;
    color: #fff
}
.circle-sm {
    width: 40px;
    padding-top: 12px;
    height: 40px;
    font-size: 14px!important
}
.circle-md {
    width: 60px;
    padding-top: 15px;
    height: 60px;
    font-size: 24px!important
}
.circle-lg {
    width: 80px;
    padding-top: 20px;
    height: 80px;
    font-size: 30px!important
}
.row-in i {
    font-size: 24px
}
.megamenu {
    left: 0;
    right: 0;
    width: 100%
}
.mega-dropdown {
    position: static!important
}
.mega-dropdown-menu {
    padding: 20px 20px 20px 80px;
    width: 100%;
    -webkit-box-shadow: none;
    border: 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, .1)!important
}
.mega-dropdown-menu>li>ul {
    padding: 0;
    margin: 0
}
.mega-dropdown-menu>li>ul>li {
    list-style: none
}
.mega-dropdown-menu>li>ul>li>a {
    display: block;
    padding: 8px 0;
    clear: both;
    line-height: 1.428571429;
    color: #313131;
    white-space: normal
}
.mega-dropdown-menu>li>ul>li>a:focus,
.mega-dropdown-menu>li>ul>li>a:hover {
    text-decoration: none;
    color: #2cabe3
}
.mega-dropdown-menu .dropdown-header {
    font-size: 16px;
    font-weight: 500;
    padding: 8px 0;
    margin-top: 12px
}
.mega-dropdown-menu li.demo-box a {
    color: #fff;
    display: block
}
.mega-dropdown-menu li.demo-box a:hover {
    opacity: .8
}
.mailbox {
    width: 280px;
    overflow: auto;
    padding-bottom: 0
}
.message-center a {
    border-bottom: 1px solid rgba(120, 130, 140, .13);
    display: block;
    padding: 9px 15px
}
.message-center a:hover {
    background: #f7fafc
}
.message-center .user-img {
    width: 40px;
    float: left;
    position: relative;
    margin: 0 10px 15px 0
}
.message-center .user-img img {
    width: 100%
}
.message-center .user-img .profile-status {
    border: 2px solid #fff;
    border-radius: 50%;
    display: inline-block;
    height: 10px;
    left: 30px;
    position: absolute;
    top: 1px;
    width: 10px
}
.message-center .user-img .online {
    background: #7ace4c
}
.message-center .user-img .busy {
    background: #f33155
}
.message-center .user-img .away,
.message-center .user-img .offline {
    background: #fb4
}
.message-center .mail-contnet h5 {
    margin: 0;
    font-weight: 400;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap
}
.message-center .mail-contnet .mail-desc {
    font-size: 12px;
    display: block;
    margin: 5px 0;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    color: #263238
}
.message-center .mail-contnet .time {
    display: block;
    font-size: 10px;
    color: #263238
}
.mail-contnet a.action {
    margin-left: 10px;
    font-size: 12px;
    visibility: hidden
}
.mail-contnet:hover a.action {
    visibility: visible
}
.inbox-center td {
    white-space: nowrap
}
.inbox-center .unread td {
    font-weight: 400
}
.inbox-center a {
    color: #313131;
    padding: 2px 0 3px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block
}
.comment-center {
    margin: 0 -25px
}
.comment-center .comment-body {
    border-bottom: 1px solid rgba(120, 130, 140, .13);
    display: table;
    width: 100%;
    padding: 20px 25px
}
.comment-center .comment-body:hover {
    background: #f7fafc
}
.comment-center .user-img {
    width: 40px;
    display: table-cell;
    position: relative;
    margin: 0 10px 0 0
}
.comment-center .user-img img {
    width: 100%
}
.comment-center .mail-contnet {
    display: table-cell;
    padding-left: 15px;
    vertical-align: top
}
.comment-center .mail-contnet h5 {
    margin-top: 0;
    font-weight: 400;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap
}
.comment-center .mail-contnet .mail-desc {
    font-size: 14px;
    display: block;
    margin: 15px 0;
    line-height: 25px;
    color: #848a96;
    overflow: hidden;
    max-height: 52px;
    text-overflow: ellipsis
}
.comment-center .mail-contnet .time {
    display: inline-block;
    font-size: 12px;
    color: #98a6ad
}
.sales-report {
    background: #f7fafc;
    margin: 12px -25px;
    padding: 15px
}
.dropdown-alerts,
.dropdown-tasks {
    padding: 0
}
.dropdown-alerts li a,
.dropdown-tasks li a,
.mailbox li>a {
    padding: 15px 20px
}
.dropdown-alerts li.divider,
.dropdown-tasks li.divider {
    margin: 0
}
.row-in-br {
    border-right: 1px solid rgba(120, 130, 140, .13)
}
.col-in {
    list-style: none;
    padding: 0;
    margin: 0
}
.col-in li {
    display: inline-block;
    vertical-align: middle;
    padding: 0 10px
}
.col-in li .circle {
    display: inline-block
}
.col-in li.col-middle {
    width: 40%
}
.col-in li.col-last {
    float: right
}
.col-in h3 {
    font-size: 36px;
    font-weight: 100
}
.basic-list {
    padding: 0
}
.basic-list li {
    display: block;
    padding: 15px 0;
    border-bottom: 1px solid rgba(120, 130, 140, .13);
    line-height: 27px
}
.basic-list li:last-child {
    border-bottom: 0
}
.steamline {
    position: relative;
    border-left: 1px solid rgba(120, 130, 140, .13);
    margin-left: 20px
}
.steamline .sl-left {
    float: left;
    margin-left: -20px;
    z-index: 1;
    width: 40px;
    line-height: 40px;
    text-align: center;
    height: 40px;
    border-radius: 100%;
    color: #fff;
    background: #263238;
    margin-right: 15px
}
.steamline .sl-left img {
    max-width: 40px
}
.steamline .sl-right {
    padding-left: 50px
}
.steamline .sl-right .desc,
.steamline .sl-right .inline-photos {
    margin-bottom: 30px
}
.steamline .sl-right div>a {
    color: #263238;
    font-weight: 400
}
.steamline .sl-item {
    border-bottom: 1px solid rgba(120, 130, 140, .13);
    margin: 20px 0
}
.sl-date {
    font-size: 10px;
    color: #98a6ad
}
.time-item {
    /*border-color: $border;*/
    padding-bottom: 1px;
    position: relative
}
.time-item:before {
    content: " ";
    display: table
}
.time-item:after {
    background-color: #fff;
    border-color: rgba(120, 130, 140, .13);
    border-radius: 10px;
    border-style: solid;
    border-width: 2px;
    bottom: 0;
    content: '';
    height: 14px;
    left: 0;
    margin-left: -8px;
    position: absolute;
    top: 5px;
    width: 14px
}
.time-item-item:after {
    content: " ";
    display: table
}
.item-info {
    margin-bottom: 15px;
    margin-left: 15px
}
.item-info p {
    margin-bottom: 10px!important
}
.user-bg {
    margin: -25px;
    height: 230px;
    overflow: hidden;
    position: relative
}
.user-bg .overlay-box {
    background: #707cd2;
    opacity: .9;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    text-align: center
}
.user-bg .overlay-box .user-content {
    padding: 15px;
    margin-top: 30px
}
.user-btm-box {
    padding: 40px 0 10px;
    clear: both;
    overflow: hidden
}
.vertical .carousel-inner {
    height: 100%;
    position: relative
}
.carousel.vertical .item {
    -webkit-transition: .6s ease-in-out top;
    -moz-transition: .6s ease-in-out top;
    -ms-transition: .6s ease-in-out top;
    -o-transition: .6s ease-in-out top;
    transition: .6s ease-in-out top
}
.carousel.vertical .active {
    top: 0
}
.carousel.vertical .next {
    top: 400px
}
.carousel.vertical .prev {
    top: -400px
}
.carousel.vertical .next.left,
.carousel.vertical .prev.right {
    top: 0
}
.carousel.vertical .active.left {
    top: -400px
}
.carousel.vertical .active.right {
    top: 400px
}
.carousel.vertical .item {
    left: 0
}
.twi-user img {
    margin-right: 20px;
    width: 50px
}
.twi-user {
    margin: 18px 0
}
.carousel-inner h3 {
    overflow: hidden
}
.carousel-inner>.item>img {
    width: 100%
}
.chart-box {
    margin: 25px -15px -17px -17px
}
.chat-list {
    list-style: none;
    padding: 0 20px
}
.chat-list li {
    margin-bottom: 24px;
    overflow: auto
}
.chat-list .chat-image {
    display: inline-block;
    float: left;
    text-align: center;
    width: 50px
}
.chat-list .chat-image img {
    border-radius: 100%;
    width: 100%
}
.chat-list .chat-text {
    background: #e5f7ff;
    border-radius: 0 8px 8px;
    display: inline-block;
    padding: 15px;
    font-size: 14px;
    position: relative
}
.chat-list .chat-text h4 {
    color: #1a2942;
    display: block;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    margin: 0;
    line-height: 15px;
    position: relative
}
.chat-list .chat-text p {
    margin: 0;
    padding-top: 3px
}
.chat-list .chat-text b {
    font-size: 10px;
    opacity: .8
}
.chat-list .chat-body {
    display: inline-block;
    float: left;
    font-size: 12px;
    margin-left: 12px;
    width: 65%
}
.chat-list .odd .chat-image {
    float: right!important
}
.chat-list .odd .chat-body {
    float: right!important;
    margin-right: 12px;
    text-align: right
}
.chat-list .odd .chat-text {
    background: #f7f7f7;
    border-radius: 8px 0 8px 8px
}
.chat-send {
    padding-left: 0;
    padding-right: 30px
}
.chat-send button {
    width: 100%
}
.weather-box .weather-top {
    overflow: hidden;
    padding: 10px 25px;
    margin: 0 -25px;
    background: #f7fafc
}
.weather-box .weather-top h2 {
    line-height: 24px
}
.weather-box .weather-top h2 small {
    font-size: 13px
}
.weather-box .weather-top .today_crnt {
    font-size: 45px;
    font-weight: 100
}
.weather-box .weather-top .today_crnt canvas {
    display: inline-block;
    margin-right: 10px;
    vertical-align: middle
}
.weather-box .weather-info {
    padding: 10px 0
}
.weather-box .weather-time {
    overflow: hidden;
    text-align: center;
    padding-top: 15px
}
.weather-box .weather-time li span {
    display: block
}
.weather-box .weather-time li canvas {
    font-size: 20px;
    margin: 10px 0
}
.demo-container {
    width: 100%;
    height: 350px
}
.demo-placeholder {
    width: 100%;
    height: 100%;
    font-size: 14px;
    line-height: 1.2em
}
.myadmin-alert {
    border-radius: 0;
    color: #fff;
    padding: 12px 30px 12px 12px;
    position: relative;
    text-align: left
}
.myadmin-alert a {
    color: inherit;
    font-weight: 600;
    text-decoration: underline
}
.myadmin-alert h4 {
    color: inherit;
    font-size: 14px;
    font-weight: 600;
    line-height: normal;
    margin: 0
}
.myadmin-alert .img {
    border-radius: 3px;
    height: 40px;
    left: 12px;
    position: absolute;
    top: 12px;
    width: 40px
}
.myadmin-alert-img {
    min-height: 64px;
    padding-left: 65px
}
.myadmin-alert-icon {
    padding-left: 20px
}
.myadmin-alert-icon i {
    padding-right: 10px
}
.myadmin-alert .closed {
    color: rgba(255, 255, 255, .5);
    font-size: 20px;
    font-weight: 500;
    padding: 4px;
    position: absolute;
    right: 3px;
    text-decoration: none;
    top: 0
}
.myadmin-alert .closed:hover {
    color: #fff
}
.myadmin-alert-click {
    cursor: pointer;
    padding-right: 12px
}
.myadmin-alert .primary {
    background: rgba(0, 0, 0, .4);
    border: none;
    border-radius: 3px;
    color: inherit;
    outline: 0;
    padding: 4px 10px
}
.myadmin-alert .cancel {
    background: rgba(255, 255, 255, .4);
    border: none;
    border-radius: 3px;
    color: rgba(0, 0, 0, .8);
    outline: 0;
    padding: 4px 10px
}
.myadmin-alert .cancel:hover,
.myadmin-alert .primary:hover {
    opacity: .9
}
.myadmin-alert-bottom,
.myadmin-alert-bottom-left,
.myadmin-alert-bottom-right,
.myadmin-alert-fullscreen,
.myadmin-alert-top,
.myadmin-alert-top-left,
.myadmin-alert-top-right {
    box-shadow: 2px 2px 2px rgba(0, 0, 0, .1);
    display: none;
    position: fixed;
    z-index: 1000
}
.myadmin-alert-top {
    left: 0;
    right: 0;
    top: 0
}
.myadmin-alert-bottom {
    bottom: 0;
    left: 0;
    right: 0
}
.myadmin-alert-top-left {
    left: 20px;
    top: 80px
}
.myadmin-alert-top-right {
    right: 20px;
    top: 80px
}
.myadmin-alert-bottom-left {
    bottom: 20px;
    left: 20px
}
.myadmin-alert-bottom-right {
    bottom: 20px;
    right: 20px
}
.myadmin-alert-fullsize {
    left: 50%;
    margin: -20px;
    top: 50%
}
.alert-custom {
    background: #2cabe3;
    color: #fff;
    border-color: #2cabe3
}
.alert-inverse {
    background: #4c5667;
    color: #fff;
    border-color: #4c5667
}
.alert-success {
    background: #7ace4c;
    color: #fff;
    border-color: #7ace4c
}
.alert-dark {
    background: #313131;
    color: #fff;
    border-color: #313131
}
.alert-warning {
    background: #fb4;
    color: #fff;
    border-color: #fb4
}
.alert-danger {
    background: #f33155;
    color: #fff;
    border-color: #f33155
}
.alert-primary {
    background: #707cd2;
    color: #fff;
    border-color: #707cd2
}
.alert-info {
    background: #41b3f9;
    color: #fff;
    border-color: #41b3f9
}
.alert-info .closed,
.alert-info a.closed:hover {
    color: inherit
}
.tab-content {
    margin-top: 30px
}
.customtab {
    border-bottom: 2px solid #f7fafc
}
.customtab li.active a,
.customtab li.active a:focus,
.customtab li.active a:hover {
    background: #fff;
    border: 0;
    border-bottom: 2px solid #2cabe3;
    margin-bottom: -1px;
    color: #2cabe3
}
.customtab li a,
.customtab li a:focus,
.customtab li a:hover {
    border: 0
}
.customtab2 {
    border-bottom: 1px solid #f7fafc;
    border-top: 1px solid #f7fafc;
    padding: 10px 0
}
.customtab2 li.active a,
.customtab2 li.active a:focus,
.customtab2 li.active a:hover {
    background: #2cabe3;
    border: 1px solid #2cabe3;
    color: #fff
}
.customtab2 li a,
.customtab2 li a:focus,
.customtab2 li a:hover {
    border: 0
}
.vtabs {
    display: table
}
.vtabs .tabs-vertical {
    width: 150px;
    border-right: 1px solid rgba(120, 130, 140, .13);
    display: table-cell;
    vertical-align: top
}
.vtabs .tabs-vertical li a {
    color: #263238;
    margin-bottom: 10px
}
.vtabs .tab-content {
    display: table-cell;
    padding: 20px;
    vertical-align: top
}
.tabs-vertical li.active a,
.tabs-vertical li.active a:focus,
.tabs-vertical li.active a:hover {
    background: #2cabe3;
    border: 0;
    border-right: 2px solid #2cabe3;
    margin-right: -1px;
    color: #fff
}
.customvtab .tabs-vertical li.active a,
.customvtab .tabs-vertical li.active a:focus,
.customvtab .tabs-vertical li.active a:hover {
    background: #fff;
    border: 0;
    border-right: 2px solid #2cabe3;
    margin-right: -1px;
    color: #263238
}
.nav-pills>li.active>a,
.nav-pills>li.active>a:focus,
.nav-pills>li.active>a:hover {
    background: #2cabe3;
    color: #fff
}
.nav-pills>li>a {
    color: #263238;
    border-radius: 0
}
.panel-group .panel .panel-heading .accordion-toggle.collapsed:before,
.panel-group .panel .panel-heading a[data-toggle=collapse].collapsed:before {
    content: '\e64b'
}
.panel-group .panel .panel-heading a[data-toggle=collapse] {
    display: block
}
.panel-group .panel .panel-heading a[data-toggle=collapse]:before {
    content: '\e648';
    display: block;
    float: right;
    font-family: themify;
    font-size: 14px;
    text-align: right;
    width: 25px
}
.panel-group .panel .panel-heading .accordion-toggle {
    display: block
}
.panel-group .panel .panel-heading .accordion-toggle:before {
    content: '\e648';
    display: block;
    float: right;
    font-family: themify;
    font-size: 14px;
    text-align: right;
    width: 25px
}
.panel-group .panel .panel-heading+.panel-collapse .panel-body {
    border-top: none
}
.panel-group .panel-heading {
    padding: 12px 20px
}
.progress {
    -webkit-box-shadow: none!important;
    background-color: rgba(120, 130, 140, .13);
    box-shadow: none!important;
    height: 4px;
    border-radius: 0;
    margin-bottom: 18px;
    overflow: hidden
}
.progress-bar {
    box-shadow: none;
    font-size: 8px;
    font-weight: 600;
    line-height: 12px
}
.progress.progress-sm {
    height: 8px!important
}
.progress.progress-sm .progress-bar {
    font-size: 8px;
    line-height: 5px
}
.progress.progress-md {
    height: 15px!important
}
.progress.progress-md .progress-bar {
    font-size: 10.8px;
    line-height: 14.4px
}
.progress.progress-lg {
    height: 20px!important
}
.progress.progress-lg .progress-bar {
    font-size: 12px;
    line-height: 20px
}
.progress-bar-primary {
    background-color: #7460ee
}
.progress-bar-success {
    background-color: #7ace4c
}
.progress-bar-info {
    background-color: #41b3f9
}
.progress-bar-megna {
    background-color: #01c0c8
}
.progress-bar-warning {
    background-color: #fb4
}
.progress-bar-danger {
    background-color: #f33155
}
.progress-bar-inverse {
    background-color: #4c5667
}
.progress-bar-purple {
    background-color: #707cd2
}
.progress-bar-custom {
    background-color: #41b3f9
}
.progress-animated {
    -webkit-animation-duration: 5s;
    -webkit-animation-name: myanimation;
    -webkit-transition: 5s all;
    animation-duration: 5s;
    animation-name: myanimation;
    transition: 5s all
}
@-webkit-keyframes myanimation {
    from {
        width: 0
    }
}
@keyframes myanimation {
    from {
        width: 0
    }
}
.progress-vertical {
    min-height: 250px;
    height: 250px;
    width: 4px;
    position: relative;
    display: inline-block;
    margin-bottom: 0;
    margin-right: 20px
}
.progress-vertical .progress-bar {
    width: 100%
}
.progress-vertical-bottom {
    min-height: 250px;
    height: 250px;
    position: relative;
    width: 4px;
    display: inline-block;
    margin-bottom: 0;
    margin-right: 20px
}
.progress-vertical-bottom .progress-bar {
    width: 100%;
    position: absolute;
    bottom: 0
}
.progress-vertical-bottom.progress-sm,
.progress-vertical.progress-sm {
    width: 8px!important
}
.progress-vertical-bottom.progress-sm .progress-bar,
.progress-vertical.progress-sm .progress-bar {
    font-size: 8px;
    line-height: 5px
}
.progress-vertical-bottom.progress-md,
.progress-vertical.progress-md {
    width: 15px!important
}
.progress-vertical-bottom.progress-md .progress-bar,
.progress-vertical.progress-md .progress-bar {
    font-size: 10.8px;
    line-height: 14.4px
}
.progress-vertical-bottom.progress-lg,
.progress-vertical.progress-lg {
    width: 20px!important
}
.progress-vertical-bottom.progress-lg .progress-bar,
.progress-vertical.progress-lg .progress-bar {
    font-size: 12px;
    line-height: 20px
}
.timeline {
    position: relative;
    padding: 20px 0;
    list-style: none;
    max-width: 1200px;
    margin: 0 auto
}
.timeline:before {
    content: " ";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50%;
    width: 3px;
    margin-left: -1.5px;
    background-color: #eee
}
.timeline>li {
    position: relative;
    margin-bottom: 20px
}
.timeline>li:after,
.timeline>li:before {
    content: " ";
    display: table
}
.timeline>li:after {
    clear: both
}
.timeline>li>.timeline-panel {
    float: left;
    position: relative;
    width: 46%;
    padding: 20px;
    border: 1px solid rgba(120, 130, 140, .13);
    border-radius: 0;
    -webkit-box-shadow: 0 1px 6px rgba(0, 0, 0, .05);
    box-shadow: 0 1px 6px rgba(0, 0, 0, .05)
}
.timeline>li>.timeline-panel:before {
    content: " ";
    display: inline-block;
    position: absolute;
    top: 26px;
    right: -8px;
    border-top: 8px solid transparent;
    border-right: 0 solid rgba(120, 130, 140, .13);
    border-bottom: 8px solid transparent;
    border-left: 8px solid rgba(120, 130, 140, .13)
}
.timeline>li>.timeline-panel:after {
    content: " ";
    display: inline-block;
    position: absolute;
    top: 27px;
    right: -7px;
    border-top: 7px solid transparent;
    border-right: 0 solid #fff;
    border-bottom: 7px solid transparent;
    border-left: 7px solid #fff
}
.timeline>li>.timeline-badge {
    z-index: 100;
    position: absolute;
    top: 16px;
    left: 50%;
    width: 50px;
    height: 50px;
    margin-left: -25px;
    border-radius: 50%;
    text-align: center;
    font-size: 1.4em;
    line-height: 50px;
    color: #fff;
    overflow: hidden;
    background-color: #4c5667
}
.timeline>li.timeline-inverted>.timeline-panel {
    float: right
}
.timeline>li.timeline-inverted>.timeline-panel:before {
    right: auto;
    left: -8px;
    border-right-width: 8px;
    border-left-width: 0
}
.timeline>li.timeline-inverted>.timeline-panel:after {
    right: auto;
    left: -7px;
    border-right-width: 7px;
    border-left-width: 0
}
.timeline-badge.primary {
    background-color: #7460ee!important
}
.timeline-badge.success {
    background-color: #7ace4c!important
}
.timeline-badge.warning {
    background-color: #fb4!important
}
.timeline-badge.danger {
    background-color: #f33155!important
}
.timeline-badge.info {
    background-color: #41b3f9!important
}
.timeline-title {
    margin-top: 0;
    color: inherit;
    font-weight: 400
}
.timeline-body>p,
.timeline-body>ul {
    margin-bottom: 0
}
.timeline-body>p+p {
    margin-top: 5px
}
.chart {
    position: relative;
    display: inline-block;
    width: 100px;
    height: 100px;
    margin-top: 20px;
    margin-bottom: 20px;
    text-align: center
}
.chart canvas {
    position: absolute;
    top: 0;
    left: 0
}
.chart.chart-widget-pie {
    margin-top: 5px;
    margin-bottom: 5px
}
.pie-chart>span {
    left: 0;
    margin-top: -2px;
    position: absolute;
    right: 0;
    text-align: center;
    top: 50%;
    transform: translateY(-50%)
}
.chart>span>img {
    left: 0;
    position: absolute;
    right: 0;
    text-align: center;
    top: 50%;
    width: 60%;
    height: 60%;
    transform: translateY(-50%);
    margin: 0 auto
}
.percent {
    display: inline-block;
    line-height: 100px;
    z-index: 2;
    font-weight: 600;
    font-size: 18px;
    color: #263238
}
.percent:after {
    content: '%';
    margin-left: .1em;
    font-size: .8em
}
.table {
    margin-bottom: 10px
}
.table-hover>tbody>tr:hover,
.table-striped>tbody>tr:nth-of-type(odd),
.table>tbody>tr.active>td,
.table>tbody>tr.active>th,
.table>tbody>tr>td.active,
.table>tbody>tr>th.active,
.table>tfoot>tr.active>td,
.table>tfoot>tr.active>th,
.table>tfoot>tr>td.active,
.table>tfoot>tr>th.active,
.table>thead>tr.active>td,
.table>thead>tr.active>th,
.table>thead>tr>td.active,
.table>thead>tr>th.active {
    background-color: #f7fafc!important
}
.table-bordered,
.table>tbody>tr>td,
.table>tbody>tr>th,
.table>tfoot>tr>td,
.table>tfoot>tr>th,
.table>thead>tr>td,
.table>thead>tr>th {
    border-top: 1px solid #e4e7ea
}
.table>tbody>tr>td,
.table>tbody>tr>th,
.table>tfoot>tr>td,
.table>tfoot>tr>th,
.table>thead>tr>td,
.table>thead>tr>th {
    padding: 15px 8px
}
.table-bordered>tbody>tr>td,
.table-bordered>tbody>tr>th,
.table-bordered>tfoot>tr>td,
.table-bordered>tfoot>tr>th,
.table-bordered>thead>tr>td,
.table-bordered>thead>tr>th {
    border: 1px solid #e4e7ea
}
.table>thead>tr>th {
    vertical-align: bottom;
    border-bottom: 1px solid #e4e7ea
}
tbody {
    color: #797979
}
th {
    color: #666;
    font-weight: 500
}
.table-bordered {
    border: 1px solid #e4e7ea
}
table.focus-on tbody tr.focused td,
table.focus-on tbody tr.focused th {
    background-color: #2cabe3;
    color: #fff
}
.table-rep-plugin .table-responsive {
    border: none!important
}
.table-rep-plugin tbody th {
    font-size: 14px;
    font-weight: 400
}
.jsgrid .jsgrid-table {
    margin-bottom: 0
}
.jsgrid-selected-row>td {
    background: #f7fafc;
    border-color: #f7fafc
}
.jsgrid-header-row>th {
    background: #fff
}
.footable-odd {
    background-color: #f7fafc
}
.form-control-line {
    border-left: 0 none;
    border-radius: 0;
    border-right: 0 none;
    border-top: 0 none;
    box-shadow: none;
    padding-left: 0
}
.has-success .form-control {
    border-color: #7ace4c;
    box-shadow: none!important
}
.has-warning .form-control {
    border-color: #fb4;
    box-shadow: none!important
}
.has-error .form-control {
    border-color: #f33155;
    box-shadow: none!important
}
.input-group-addon {
    border-radius: 2px;
    border: 1px solid rgba(120, 130, 140, .13)
}
.input-daterange input:first-child,
.input-daterange input:last-child {
    border-radius: 0
}
.form-material .form-group {
    overflow: hidden
}
.form-material .form-control {
    background-color: rgba(0, 0, 0, 0);
    background-position: center bottom, center calc(99%);
    background-repeat: no-repeat;
    background-size: 0 2px, 100% 1px;
    padding: 0;
    transition: background 0s ease-out 0s
}
.form-material .form-control,
.form-material .form-control.focus,
.form-material .form-control:focus {
    background-image: linear-gradient(#707cd2, #707cd2), linear-gradient(rgba(120, 130, 140, .13), rgba(120, 130, 140, .13));
    border: 0;
    border-radius: 0;
    box-shadow: none;
    float: none
}
.form-material .form-control.focus,
.form-material .form-control:focus {
    background-size: 100% 2px, 100% 1px;
    outline: 0;
    transition-duration: .3s
}
.form-bordered .form-group {
    border-bottom: 1px solid rgba(120, 130, 140, .13);
    padding-bottom: 20px
}
.select2-container .select2-choice {
    background-image: none!important;
    border: none!important;
    height: auto!important;
    padding: 0!important;
    line-height: 22px!important;
    background-color: transparent!important;
    box-shadow: none!important
}
.select2-container .select2-choice .select2-arrow {
    background-image: none!important;
    background: 0 0;
    border: none;
    width: 14px;
    top: -2px
}
.select2-container .select2-container-multi.form-control {
    height: auto
}
.select2-results .select2-highlighted {
    color: #fff;
    background-color: #41b3f9
}
.select2-drop-active {
    border: 1px solid #e3e3e3!important;
    padding-top: 5px
}
.select2-search input {
    border: 1px solid rgba(120, 130, 140, .13)
}
.select2-container-multi {
    width: 100%
}
.select2-container-multi .select2-choices {
    border: 1px solid #border!important;
    box-shadow: none!important;
    background-image: none!important;
    border-radius: 0!important;
    min-height: 38px
}
.select2-container-multi .select2-choices .select2-search-choice {
    padding: 4px 7px 4px 18px;
    margin: 5px 0 3px 5px;
    color: #555;
    background: #f5f5f5;
    border-color: rgba(120, 130, 140, .13);
    -webkit-box-shadow: none;
    box-shadow: none
}
.select2-container-multi .select2-choices .select2-search-field input {
    padding: 7px 7px 7px 10px;
    font-family: inherit
}
.icon-list-demo div {
    cursor: pointer;
    line-height: 60px;
    white-space: nowrap;
    color: #313131
}
.icon-list-demo div:hover {
    color: #263238
}
.icon-list-demo div p {
    margin: 10px 0;
    padding: 5px 0
}
.icon-list-demo i {
    -webkit-transition: all .2s;
    -webkit-transition: font-size .2s;
    display: inline-block;
    font-size: 18px;
    margin: 0 15px 0 10px;
    text-align: left;
    vertical-align: middle;
    width: auto;
    transition: all .3s ease 0s
}
.icon-list-demo .col-md-4 {
    border-radius: 0
}
.icon-list-demo .col-md-4:hover {
    background-color: #f7fafc
}
.icon-list-demo .col-md-4:hover i {
    font-size: 2em
}
.gmaps,
.gmaps-panaroma {
    height: 300px;
    background: #e4e7ea;
    border-radius: 3px
}
.gmaps-overlay {
    display: block;
    text-align: center;
    color: #fff;
    font-size: 16px;
    line-height: 40px;
    background: #7460ee;
    border-radius: 4px;
    padding: 10px 20px
}
.gmaps-overlay_arrow {
    left: 50%;
    margin-left: -16px;
    width: 0;
    height: 0;
    position: absolute
}
.gmaps-overlay_arrow.above {
    bottom: -15px;
    border-left: 16px solid transparent;
    border-right: 16px solid transparent;
    border-top: 16px solid #7460ee
}
.gmaps-overlay_arrow.below {
    top: -15px;
    border-left: 16px solid transparent;
    border-right: 16px solid transparent;
    border-bottom: 16px solid #7460ee
}
.jvectormap-zoomin,
.jvectormap-zoomout {
    width: 10px;
    height: 10px;
    line-height: 10px
}
.jvectormap-zoomout {
    top: 40px
}
.error-box {
    height: 100%;
    position: fixed;
    top: 20%;
    width: 100%
}
.error-box .footer {
    width: 100%;
    left: 0;
    right: 0
}
.error-body {
    padding-top: 5%
}
.error-body h1 {
    font-size: 210px;
    font-weight: 900;
    line-height: 210px
}
.login-register {
    /*background: url(../../plugins/images/login-register.jpg) center center/cover no-repeat!important;*/
    height: 100%;
    position: fixed
}
.login-box {
    background: #fff;
    width: 400px;
    margin: 10% auto 0
}
.login-box .footer {
    width: 100%;
    left: 0;
    right: 0
}
.login-box .social {
    display: block;
    margin-bottom: 30px
}
#recoverform {
    display: none
}
.new-login-register {
    position: fixed;
    height: 100%
}
.new-login-register .lg-info-panel {
    /*background: url(../../plugins/images/login-register.jpg) center center/cover no-repeat!important;*/
    width: 500px;
    height: 100%;
    position: fixed
}
.new-login-register .lg-info-panel .inner-panel {
    position: absolute;
    height: 100%;
    width: 100%;
    background: rgba(0, 0, 0, .5)
}
.new-login-register .lg-info-panel .lg-content {
    margin-top: 50%;
    text-align: center;
    padding: 0 50px
}
.new-login-register .lg-info-panel .lg-content h2 {
    color: #fff
}
.new-login-register .lg-info-panel .lg-content p {
    padding: 20px 0;
    color: rgba(255, 255, 255, .7);
    font-style: italic
}
.new-login-register .new-login-box {
    margin-left: 50%;
    margin-top: 10%;
    width: 400px
}
.new-login-register .new-login-box .new-lg-form {
    padding-top: 20px
}
.new-login-register .new-login-box .new-lg-form label {
    text-transform: uppercase;
    font-size: 12px
}
.new-login-register .new-login-box .social {
    display: block;
    margin-bottom: 30px
}
.pricing-box {
    position: relative;
    text-align: center;
    margin-top: 30px
}
.featured-plan {
    margin-top: 0
}
.featured-plan .pricing-body {
    padding: 60px 0;
    background: #f7fafc;
    border: 1px solid #ddd
}
.featured-plan .price-table-content .price-row {
    border-top: 1px solid rgba(120, 130, 140, .13)
}
.pricing-body {
    border-radius: 0;
    border-top: 1px solid rgba(120, 130, 140, .13);
    border-bottom: 5px solid rgba(120, 130, 140, .13);
    vertical-align: middle;
    padding: 30px 0;
    position: relative
}
.pricing-body h2 {
    position: relative;
    font-size: 56px;
    margin: 20px 0 10px;
    font-weight: 500
}
.pricing-body h2 span {
    position: absolute;
    font-size: 15px;
    top: -10px;
    margin-left: -10px
}
.price-table-content .price-row {
    padding: 20px 0;
    border-top: 1px solid rgba(120, 130, 140, .13)
}
.pricing-plan {
    padding: 0 15px
}
.pricing-plan .no-padding {
    padding: 0
}
.price-lable {
    position: absolute;
    top: -10px;
    padding: 5px 10px;
    margin: 0 auto;
    display: inline-block;
    width: 100px;
    left: 0;
    right: 0
}
.mails a {
    color: #263238
}
.mails td {
    vertical-align: middle!important;
    position: relative
}
.mails td:last-of-type {
    width: 100px;
    padding-right: 20px
}
.mails tr:hover .text-white {
    display: none
}
.mails .mail-select {
    padding: 12px 20px;
    min-width: 134px
}
.mails .checkbox {
    margin-bottom: 0;
    margin-top: 0;
    vertical-align: middle;
    display: inline-block;
    height: 17px
}
.mails .checkbox label {
    min-height: 16px
}
.mail-list .list-group-item {
    background-color: transparent;
    border: 0;
    border-left: 3px solid #fff;
    border-radius: 0
}
.mail-list .list-group-item:hover {
    background: #f7fafc;
    border-left: 3px solid #f7fafc
}
.mail-list .list-group-item:focus {
    border-left: 3px solid #f7fafc
}
.mail-list .list-group-item.active:focus {
    background: #f7fafc;
    border-left: 3px solid #f33155
}
.mail-list .list-group-item.active {
    border-left: 3px solid #f33155;
    border-radius: 0;
    color: #263238!important
}
.mail_listing {
    min-height: 500px
}
.inbox_listing .inbox-item:hover {
    background: #f7fafc
}
.inbox_listing .inbox-item {
    padding-left: 20px
}
.inbox-widget.inbox_listing .inbox-item .inbox-item-text {
    height: 19px;
    overflow: hidden
}
.message-center .unread .mail-contnet .mail-desc,
.message-center .unread .mail-contnet h5 {
    font-weight: 600;
    color: #263238!important
}
.calendar {
    float: left;
    margin-bottom: 0
}
.fc-view {
    margin-top: 30px
}
.none-border .modal-footer {
    border-top: none
}
.fc-toolbar h2 {
    font-size: 18px;
    font-weight: 600;
    line-height: 30px;
    text-transform: uppercase
}
.fc-day {
    background: #fff
}
.fc-toolbar .fc-state-active,
.fc-toolbar .ui-state-active,
.fc-toolbar .ui-state-hover,
.fc-toolbar button:focus,
.fc-toolbar button:hover {
    z-index: 0
}
.fc-widget-header {
    border: 0!important
}
.fc-widget-content {
    border-color: rgba(120, 130, 140, .13)!important
}
.fc th.fc-widget-header {
    color: #fff;
    font-size: 14px;
    line-height: 20px;
    padding: 7px 0;
    text-transform: uppercase
}
.fc th.fc-sat,
.fc th.fc-sun,
.fc th.fc-thu,
.fc th.fc-tue {
    background: #34b6ef
}
.fc th.fc-fri,
.fc th.fc-mon,
.fc th.fc-wed {
    background: #3bbcf5
}
.fc-view {
    margin-top: 0
}
.fc-toolbar {
    background: #41b3f9;
    margin: 0;
    padding: 24px 20px
}
.fc-toolbar h2 {
    color: #fff
}
.fc-button {
    background: #3bbcf5;
    border: 1px solid #41b3f9;
    color: #fff;
    text-transform: capitalize
}
.fc-button:hover {
    background: #3bbcf5;
    opacity: .8
}
.fc-text-arrow {
    font-family: inherit;
    font-size: 16px
}
.fc-state-hover {
    background: #F5F5F5
}
.fc-unthemed .fc-today {
    border: 1px solid #f33155;
    background: #f7fafc!important
}
.fc-cell-overlay,
.fc-state-highlight {
    background: #f0f0f0
}
.fc-event {
    border-radius: 0;
    border: none;
    cursor: move;
    font-size: 13px;
    margin: 1px -1px 0;
    padding: 5px;
    text-align: center;
    background: #41b3f9
}
.calendar-event {
    cursor: move;
    margin: 10px 5px 0 0;
    padding: 6px 10px;
    display: inline-block;
    color: #fff;
    min-width: 140px;
    text-align: center;
    background: #41b3f9
}
.calendar-event a {
    float: right;
    opacity: .6;
    font-size: 10px;
    margin: 4px 0 0 10px;
    color: #fff
}
.fc-basic-view td.fc-week-number span {
    padding-right: 5px
}
.fc-basic-view .fc-day-number {
    padding: 10px 15px;
    display: inline-block
}
.weather h1 {
    color: #fff;
    font-size: 50px;
    font-weight: 100
}
.weather i {
    color: #fff;
    font-size: 40px
}
.weather .w-title-sub {
    color: rgba(255, 255, 255, .6)
}
@-webkit-keyframes rotate {
    from {
        -webkit-transform: rotate(0deg)
    }
    to {
        -webkit-transform: rotate(360deg)
    }
}
@-moz-keyframes rotate {
    from {
        -moz-transform: rotate(0deg)
    }
    to {
        -moz-transform: rotate(360deg)
    }
}
@keyframes rotate {
    from {
        transform: rotate(0deg)
    }
    to {
        transform: rotate(360deg)
    }
}
.right-side-toggle {
    position: relative
}
.right-side-toggle i {
    -webkit-transition-property: -webkit-transform;
    -webkit-transition-duration: 1s;
    -moz-transition-property: -moz-transform;
    -moz-transition-duration: 1s;
    transition-property: transform;
    transition-duration: 1s;
    -webkit-animation-name: rotate;
    -webkit-animation-duration: 2s;
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-timing-function: linear;
    -moz-animation-name: rotate;
    -moz-animation-duration: 2s;
    -moz-animation-iteration-count: infinite;
    -moz-animation-timing-function: linear;
    animation-name: rotate;
    animation-duration: 2s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
    position: absolute;
    top: 7px;
    left: 8px
}
.right-sidebar {
    position: fixed;
    right: -240px;
    width: 240px;
    display: none;
    z-index: 1200;
    background: #fff;
    top: 0;
    height: 100%;
    box-shadow: 5px 1px 40px rgba(0, 0, 0, .1);
    transition: all .3s ease
}
.right-sidebar .rpanel-title {
    display: block;
    padding: 21px;
    color: #fff;
    text-transform: uppercase;
    font-size: 13px;
    background: #2cabe3
}
.right-sidebar .rpanel-title span {
    float: right;
    cursor: pointer;
    font-size: 11px
}
.right-sidebar .rpanel-title span:hover {
    color: #263238
}
.right-sidebar .r-panel-body {
    padding: 20px
}
.right-sidebar .r-panel-body ul {
    margin: 0;
    padding: 0
}
.right-sidebar .r-panel-body ul li {
    list-style: none;
    padding: 5px 0
}
.shw-rside {
    right: 0;
    width: 240px;
    display: block
}
.chatonline img {
    margin-right: 10px;
    float: left;
    width: 30px
}
.chatonline li a {
    padding: 13px 0;
    float: left;
    width: 100%
}
.chatonline li a span {
    color: #313131
}
.chatonline li a span small {
    display: block;
    font-size: 10px
}
ul#themecolors {
    display: block
}
ul#themecolors li {
    display: inline-block
}
ul#themecolors li:first-child {
    display: block
}
#themecolors li a {
    width: 50px;
    height: 50px;
    display: inline-block;
    margin: 5px;
    color: transparent;
    position: relative
}
#themecolors li a.working:before {
    content: "\f00c";
    font-family: FontAwesome;
    font-size: 18px;
    line-height: 50px;
    width: 50px;
    height: 50px;
    position: absolute;
    top: 0;
    left: 0;
    color: #fff;
    text-align: center
}
.default-theme {
    background: #4c5667
}
.green-theme {
    background: #7ace4c
}
.yellow-theme {
    background: #a0aec4
}
.blue-theme {
    background: #41b3f9
}
.purple-theme {
    background: #707cd2
}
.megna-theme {
    background: #e4e7ea
}
.default-dark-theme {
    background: #4f5467;
    background: -moz-linear-gradient(left, #4f5467 0, #4f5467 23%, #f33155 23%, #f33155 99%);
    background: -webkit-linear-gradient(left, #4f5467 0, #4f5467 23%, #f33155 23%, #f33155 99%);
    background: linear-gradient(to right, #4f5467 0, #4f5467 23%, #f33155 23%, #f33155 99%)
}
.green-dark-theme {
    background: #4f5467;
    background: -moz-linear-gradient(left, #4f5467 0, #4f5467 23%, #00c292 23%, #00c292 99%);
    background: -webkit-linear-gradient(left, #4f5467 0, #4f5467 23%, #00c292 23%, #00c292 99%);
    background: linear-gradient(to right, #4f5467 0, #4f5467 23%, #00c292 23%, #00c292 99%)
}
.yellow-dark-theme {
    background: #4f5467;
    background: -moz-linear-gradient(left, #4f5467 0, #4f5467 23%, #a0aec4 23%, #a0aec4 99%);
    background: -webkit-linear-gradient(left, #4f5467 0, #4f5467 23%, #a0aec4 23%, #a0aec4 99%);
    background: linear-gradient(to right, #4f5467 0, #4f5467 23%, #a0aec4 23%, #a0aec4 99%)
}
.blue-dark-theme {
    background: #4f5467;
    background: -moz-linear-gradient(left, #4f5467 0, #4f5467 23%, #41b3f9 23%, #41b3f9 99%);
    background: -webkit-linear-gradient(left, #4f5467 0, #4f5467 23%, #41b3f9 23%, #41b3f9 99%);
    background: linear-gradient(to right, #4f5467 0, #4f5467 23%, #41b3f9 23%, #41b3f9 99%)
}
.purple-dark-theme {
    background: #4f5467;
    background: -moz-linear-gradient(left, #4f5467 0, #4f5467 23%, #707cd2 23%, #707cd2 99%);
    background: -webkit-linear-gradient(left, #4f5467 0, #4f5467 23%, #707cd2 23%, #707cd2 99%);
    background: linear-gradient(to right, #4f5467 0, #4f5467 23%, #707cd2 23%, #707cd2 99%)
}
.megna-dark-theme {
    background: #4f5467;
    background: -moz-linear-gradient(left, #4f5467 0, #4f5467 23%, #e4e7ea 23%, #e4e7ea 99%);
    background: -webkit-linear-gradient(left, #4f5467 0, #4f5467 23%, #e4e7ea 23%, #e4e7ea 99%);
    background: linear-gradient(to right, #4f5467 0, #4f5467 23%, #e4e7ea 23%, #e4e7ea 99%)
}
.red-dark-theme {
    background: #e20b0b;
    background: -moz-linear-gradient(left, #4f5467 0, #4f5467 23%, #e20b0b 23%, #e20b0b 99%);
    background: -webkit-linear-gradient(left, #4f5467 0, #4f5467 23%, #e20b0b 23%, #e20b0b 99%);
    background: linear-gradient(to right, #4f5467 0, #4f5467 23%, #e20b0b 23%, #e20b0b 99%)
}
.visited li a {
    color: #313131
}
.visited li.active a {
    color: #2cabe3
}
.stats-row {
    margin-bottom: 20px
}
.stat-item {
    display: inline-block;
    padding-right: 15px
}
.stat-item+.stat-item {
    padding-left: 15px;
    border-left: 1px solid #eee
}
.country-state {
    list-style: none;
    margin: 0;
    padding: 0 0 0 10px
}
.country-state h2 {
    margin: 0
}
.country-state .progress {
    margin-top: 8px
}
.two-part li {
    width: 48.8%
}
.two-part li i {
    font-size: 50px
}
.two-part li span {
    font-size: 50px;
    font-weight: 100;
    font-family: Rubik, sans-serif
}
.news-slide {
    position: relative
}
.news-slide .overlaybg {
    height: 370px;
    overflow: hidden
}
.news-slide .overlaybg img {
    width: 100%;
    height: 100%
}
.news-slide .news-content {
    position: absolute;
    height: 370px;
    background: rgba(0, 0, 0, .5);
    z-index: 10;
    width: 100%;
    top: 0;
    padding: 30px
}
.news-slide .news-content h2 {
    height: 240px;
    overflow: hidden;
    color: #fff
}
.news-slide .news-content a {
    color: #fff;
    opacity: .6;
    text-transform: uppercase
}
.news-slide .news-content a:hover {
    opacity: 1
}
.dashboard-slide .news-content,
.dashboard-slide .overlaybg {
    height: 435px
}
.dashboard-slide .news-content h2 {
    height: 320px
}
.nav-pills-rounded li {
    display: inline-block;
    float: none
}
.nav-pills-rounded li a {
    border-radius: 60px;
    -moz-border-radius: 60px;
    -webkit-border-radius: 60px;
    color: #313131;
    padding: 10px 25px
}
.nav-pills-rounded li.active a,
.nav-pills-rounded li.active a:focus,
.nav-pills-rounded li.active a:hover {
    background: #2cabe3;
    color: #fff
}
.analytics-info .list-inline {
    margin-bottom: 0
}
.analytics-info .list-inline li {
    vertical-align: middle;
    display: block;
    margin-right: 0;
}
.analytics-info .list-inline li span {
    font-size: 24px
}
.analytics-info .list-inline li i {
    font-size: 20px
}
.feeds {
    margin: 0;
    padding: 0
}
.feeds li {
    list-style: none;
    padding: 10px;
    display: block
}
.feeds li:hover {
    background: #f7fafc
}
.feeds li>div {
    width: 40px;
    height: 40px;
    margin-right: 5px;
    display: inline-block;
    text-align: center;
    vertical-align: middle;
    border-radius: 100%
}
.feeds li>div i {
    line-height: 40px
}
.feeds li span {
    float: right;
    width: auto;
    font-size: 12px
}
.jq-icon-info {
    background-color: #41b3f9;
    color: #fff
}
.jq-icon-success {
    background-color: #7ace4c;
    color: #fff
}
.jq-icon-error {
    background-color: #f33155;
    color: #fff
}
.jq-icon-warning {
    background-color: #fb4;
    color: #fff
}
.dropzone {
    border-style: dashed;
    border-width: 1px
}
.weather h1 sup {
    font-size: 20px;
    top: -1.2em
}
.fcbtn {
    position: relative;
    -webkit-transition: all .3s;
    -moz-transition: all .3s;
    transition: all .3s;
    padding: 8px 20px
}
.fcbtn:after {
    content: '';
    position: absolute;
    z-index: -1;
    -webkit-transition: all .3s;
    -moz-transition: all .3s;
    transition: all .3s
}
.btn-1b:after {
    width: 100%;
    height: 0;
    top: 0;
    left: 0
}
.btn-1b:active,
.btn-1b:hover {
    color: #fff
}
.btn-1b:active:after,
.btn-1b:hover:after {
    height: 100%
}
.btn-1b.btn-info:after,
.btn-1c.btn-info:after,
.btn-1d.btn-info:after,
.btn-1e.btn-info:after,
.btn-1f.btn-info:after {
    background: #41b3f9
}
.btn-1b.btn-warning:after,
.btn-1c.btn-warning:after,
.btn-1d.btn-warning:after,
.btn-1e.btn-warning:after,
.btn-1f.btn-warning:after {
    background: #fb4
}
.btn-1b.btn-danger:after,
.btn-1c.btn-danger:after,
.btn-1d.btn-danger:after,
.btn-1e.btn-danger:after,
.btn-1f.btn-danger:after {
    background: #f33155
}
.btn-1b.btn-primary:after,
.btn-1c.btn-primary:after,
.btn-1d.btn-primary:after,
.btn-1e.btn-primary:after,
.btn-1f.btn-primary:after {
    background: #707cd2
}
.btn-1b.btn-success:after,
.btn-1c.btn-success:after,
.btn-1d.btn-success:after,
.btn-1e.btn-success:after,
.btn-1f.btn-success:after {
    background: #7ace4c
}
.btn-1b.btn-inverse:after,
.btn-1c.btn-inverse:after,
.btn-1d.btn-inverse:after,
.btn-1e.btn-inverse:after,
.btn-1f.btn-inverse:after {
    background: #4c5667
}
.btn-1c:after {
    width: 0;
    height: 100%;
    top: 0;
    left: 0
}
.btn-1c:active,
.btn-1c:hover {
    color: #000
}
.btn-1c:active:after,
.btn-1c:hover:after {
    width: 100%
}
.btn-1d {
    overflow: hidden
}
.btn-1d:after {
    width: 0;
    height: 103%;
    top: 50%;
    left: 50%;
    opacity: 0;
    -webkit-transform: translateX(-50%) translateY(-50%);
    -moz-transform: translateX(-50%) translateY(-50%);
    -ms-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%)
}
.btn-1d:hover:after {
    width: 100%;
    opacity: 1
}
.btn-1e {
    overflow: hidden
}
.btn-1e:after {
    width: 100%;
    height: 0;
    top: 50%;
    left: 50%;
    background: #fff;
    opacity: 0;
    -webkit-transform: translateX(-50%) translateY(-50%) rotate(45deg);
    -moz-transform: translateX(-50%) translateY(-50%) rotate(45deg);
    -ms-transform: translateX(-50%) translateY(-50%) rotate(45deg);
    transform: translateX(-50%) translateY(-50%) rotate(45deg)
}
.btn-1e:hover:after {
    height: 260%;
    opacity: 1
}
.btn-1e:active:after {
    height: 400%;
    opacity: 1
}
.btn-1f {
    overflow: hidden
}
.btn-1f:after {
    width: 101%;
    height: 0;
    top: 50%;
    left: 50%;
    background: #fff;
    opacity: 0;
    -webkit-transform: translateX(-50%) translateY(-50%);
    -moz-transform: translateX(-50%) translateY(-50%);
    -ms-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%)
}
.btn-1f:hover:after {
    height: 100%;
    opacity: 1
}
.btn-1f:active:after {
    height: 130%;
    opacity: 1
}
.sweet-alert {
    padding: 25px
}
.sweet-alert h2 {
    margin-top: 0
}
.sweet-alert p {
    line-height: 30px
}
ul.list-icons {
    margin: 0;
    padding: 0
}
ul.list-icons li {
    list-style: none;
    line-height: 40px
}
ul.list-icons li i {
    font-size: 12px;
    margin-right: 5px
}
.demo-popover .popover,
.demo-tooltip .tooltip {
    position: relative;
    margin-right: 25px;
    opacity: 1;
    display: inline-block
}
.tooltip-inner {
    border-radius: 3px;
    padding: 5px 10px
}
.tooltip.in {
    opacity: 1
}
.tooltip-primary+.tooltip .tooltip-inner,
.tooltip-primary.tooltip .tooltip-inner {
    color: #fff;
    background-color: #7460ee
}
.tooltip-primary+.tooltip.top .tooltip-arrow,
.tooltip-primary.tooltip.top .tooltip-arrow {
    border-top-color: #7460ee
}
.tooltip-primary+.tooltip.right .tooltip-arrow,
.tooltip-primary.tooltip.right .tooltip-arrow {
    border-right-color: #7460ee
}
.tooltip-primary+.tooltip.bottom .tooltip-arrow,
.tooltip-primary.tooltip.bottom .tooltip-arrow {
    border-bottom-color: #7460ee
}
.tooltip-primary+.tooltip.left .tooltip-arrow,
.tooltip-primary.tooltip.left .tooltip-arrow {
    border-left-color: #7460ee
}
.tooltip-success+.tooltip .tooltip-inner,
.tooltip-success.tooltip .tooltip-inner {
    color: #fff;
    background-color: #7ace4c
}
.tooltip-success+.tooltip.top .tooltip-arrow,
.tooltip-success.tooltip.top .tooltip-arrow {
    border-top-color: #7ace4c
}
.tooltip-success+.tooltip.right .tooltip-arrow,
.tooltip-success.tooltip.right .tooltip-arrow {
    border-right-color: #7ace4c
}
.tooltip-success+.tooltip.bottom .tooltip-arrow,
.tooltip-success.tooltip.bottom .tooltip-arrow {
    border-bottom-color: #7ace4c
}
.tooltip-success+.tooltip.left .tooltip-arrow,
.tooltip-success.tooltip.left .tooltip-arrow {
    border-left-color: #7ace4c
}
.tooltip-warning+.tooltip .tooltip-inner,
.tooltip-warning.tooltip .tooltip-inner {
    color: #fff;
    background-color: #fb4
}
.tooltip-warning+.tooltip.top .tooltip-arrow,
.tooltip-warning.tooltip.top .tooltip-arrow {
    border-top-color: #fb4
}
.tooltip-warning+.tooltip.right .tooltip-arrow,
.tooltip-warning.tooltip.right .tooltip-arrow {
    border-right-color: #fb4
}
.tooltip-warning+.tooltip.bottom .tooltip-arrow,
.tooltip-warning.tooltip.bottom .tooltip-arrow {
    border-bottom-color: #fb4
}
.tooltip-warning+.tooltip.left .tooltip-arrow,
.tooltip-warning.tooltip.left .tooltip-arrow {
    border-left-color: #fb4
}
.tooltip-info+.tooltip .tooltip-inner,
.tooltip-info.tooltip .tooltip-inner {
    color: #fff;
    background-color: #41b3f9
}
.tooltip-info+.tooltip.top .tooltip-arrow,
.tooltip-info.tooltip.top .tooltip-arrow {
    border-top-color: #41b3f9
}
.tooltip-info+.tooltip.right .tooltip-arrow,
.tooltip-info.tooltip.right .tooltip-arrow {
    border-right-color: #41b3f9
}
.tooltip-info+tooltip.bottom .tooltip-arrow,
.tooltip-info.tooltip.bottom .tooltip-arrow {
    border-bottom-color: #41b3f9
}
.tooltip-info+.tooltip.left .tooltip-arrow,
.tooltip-info.tooltip.left .tooltip-arrow {
    border-left-color: #41b3f9
}
.tooltip-danger+.tooltip .tooltip-inner,
.tooltip-danger.tooltip .tooltip-inner {
    color: #fff;
    background-color: #f33155
}
.tooltip-danger+.tooltip.top .tooltip-arrow,
.tooltip-danger.tooltip.top .tooltip-arrow {
    border-top-color: #f33155
}
.tooltip-danger+.tooltip.right .tooltip-arrow,
.tooltip-danger.tooltip.right .tooltip-arrow {
    border-right-color: #f33155
}
.tooltip-danger+.tooltip.bottom .tooltip-arrow,
.tooltip-danger.tooltip.bottom .tooltip-arrow {
    border-bottom-color: #f33155
}
.tooltip-danger+.tooltip.left .tooltip-arrow,
.tooltip-danger.tooltip.left .tooltip-arrow {
    border-left-color: #f33155
}
.flotTip {
    padding: 8px 12px;
    background-color: #263238;
    z-index: 100;
    color: #fff;
    opacity: .9;
    font-size: 13px
}
.popover {
    -webkit-box-shadow: 0 2px 6px rgba(0, 0, 0, .05);
    box-shadow: 0 2px 6px rgba(0, 0, 0, .05)
}
.popover .popover-title {
    border-radius: 0
}
.popover-primary+.popover .popover-title {
    color: #fff;
    background-color: #7460ee;
    border-color: #7460ee
}
.popover-primary+.popover.bottom .arrow,
.popover-primary+.popover.bottom .arrow:after {
    border-bottom-color: #7460ee
}
.popover-success+.popover .popover-title {
    color: #fff;
    background-color: #7ace4c;
    border-color: #7ace4c
}
.popover-success+.popover.bottom .arrow,
.popover-success+.popover.bottom .arrow:after {
    border-bottom-color: #7ace4c
}
.popover-info+.popover .popover-title {
    color: #fff;
    background-color: #41b3f9;
    border-color: #41b3f9
}
.popover-info+.popover.bottom .arrow,
.popover-info+.popover.bottom .arrow:after {
    border-bottom-color: #41b3f9
}
.popover-warning+.popover .popover-title {
    color: #fff;
    background-color: #fb4;
    border-color: #fb4
}
.popover-warning+.popover.bottom .arrow,
.popover-warning+.popover.bottom .arrow:after {
    border-bottom-color: #fb4
}
.popover-danger+.popover .popover-title {
    color: #fff;
    background-color: #f33155;
    border-color: #f33155
}
.popover-danger+.popover.bottom .arrow,
.popover-danger+.popover.bottom .arrow:after {
    border-bottom-color: #f33155
}
.btn-file {
    overflow: hidden;
    position: relative;
    vertical-align: middle
}
.btn-file>input {
    position: absolute;
    top: 0;
    right: 0;
    margin: 0;
    opacity: 0;
    filter: alpha(opacity=0);
    font-size: 23px;
    height: 100%;
    width: 100%;
    direction: ltr;
    cursor: pointer;
    border-radius: 0
}
.fileinput {
    margin-bottom: 9px;
    display: inline-block
}
.fileinput .form-control {
    padding-top: 7px;
    padding-bottom: 5px;
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
    cursor: text
}
.fileinput .thumbnail {
    overflow: hidden;
    display: inline-block;
    margin-bottom: 5px;
    vertical-align: middle;
    text-align: center
}
.fileinput .thumbnail>img {
    max-height: 100%
}
.fileinput .btn {
    vertical-align: middle
}
.fileinput-exists .fileinput-new,
.fileinput-new .fileinput-exists {
    display: none
}
.fileinput-inline .fileinput-controls {
    display: inline
}
.fileinput-filename {
    vertical-align: middle;
    display: inline-block;
    overflow: hidden
}
.form-control .fileinput-filename {
    vertical-align: bottom
}
.fileinput.input-group {
    display: table
}
.fileinput.input-group>* {
    position: relative;
    z-index: 2
}
.fileinput.input-group>.btn-file {
    z-index: 1
}
.bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
    width: 100%
}
.ms-container .ms-list {
    border-radius: 0;
    box-shadow: none
}
.ms-container .ms-selectable li.ms-elem-selectable,
.ms-container .ms-selection li.ms-elem-selection {
    padding: 6px 10px
}
.ms-container .ms-selectable li.ms-hover,
.ms-container .ms-selection li.ms-hover {
    background: #41b3f9
}
.dropzone .dz-message {
    text-align: center;
    margin: 10% 0
}
.editable-input .form-control {
    height: 30px
}
.asColorPicker-trigger {
    position: absolute;
    top: 0;
    right: -35px;
    height: 38px;
    width: 37px;
    border: 0
}
.asColorPicker-dropdown {
    max-width: 260px
}
.asColorPicker-clear {
    top: 7px;
    right: 16px
}
.datepicker table tr td.today,
.datepicker table tr td.today.disabled,
.datepicker table tr td.today.disabled:hover,
.datepicker table tr td.today:hover {
    background: #2cabe3;
    color: #fff
}
.datepicker table tr td.active,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active.disabled:hover,
.datepicker table tr td.active:hover {
    background: #41b3f9;
    color: #fff
}
.editable-table+input.error {
    border: 1px solid #danger;
    outline: 0;
    outline-offset: 0
}
#editable-datatable_wrapper+input:focus,
.editable-table+input,
.editable-table+input:focus {
    border: 1px solid #41b3f9!important;
    outline: 0!important;
    outline-offset: 0!important
}
.editable-table td:focus {
    outline: 0
}
.user-profile {
    padding: 70px 0 15px;
    position: relative;
    text-align: center
}
.user-profile .user-pro-body {
    display: block
}
.user-profile .user-pro-body img {
    width: 50px;
    display: block;
    margin: 0 auto 10px
}
.user-profile .user-pro-body .u-dropdown {
    color: #97999f
}
.user-profile .user-pro-body .dropdown-menu {
    right: 0;
    width: 180px;
    left: 0;
    margin: 0 auto
}
.wizard-steps {
    display: table;
    width: 100%
}
.wizard-steps>li {
    display: table-cell;
    padding: 10px 20px;
    background: #f7fafc
}
.wizard-steps>li span {
    border-radius: 100%;
    border: 1px solid rgba(120, 130, 140, .13);
    width: 40px;
    height: 40px;
    display: inline-block;
    vertical-align: middle;
    padding-top: 9px;
    margin-right: 8px;
    text-align: center
}
.wizard-content {
    padding: 25px;
    border-color: rgba(120, 130, 140, .13);
    margin-bottom: 30px
}
.wizard-steps>li.current,
.wizard-steps>li.done {
    background: #41b3f9;
    color: #fff
}
.wizard-steps>li.current span,
.wizard-steps>li.done span {
    border-color: #fff;
    color: #fff
}
.wizard-steps>li.current h4,
.wizard-steps>li.done h4 {
    color: #fff
}
.wizard-steps>li.done {
    background: #7ace4c
}
.wizard-steps>li.error {
    background: #f33155
}
.wiz-aco .pager {
    margin: 0
}
#morris-donut-chart svg text {
    font-family: Rubik, sans-serif!important;
    font-weight: 400!important
}
#diagram {
    margin: 0 auto;
    width: 250px;
    padding-top: 30px;
    height: 271px
}
#diagram circle {
    fill: #fff
}
#diagram text {
    fill: #313131
}
.get {
    display: none
}
ul.expense-box {
    margin: 0;
    padding: 0
}
ul.expense-box li {
    list-style: none;
    display: inline-block;
    padding: 8px 0 8px 20px
}
ul.expense-box li i {
    width: 60px;
    font-size: 30px;
    vertical-align: middle;
    display: inline-block
}
ul.expense-box li div,
ul.expense-box li span {
    display: inline-block;
    vertical-align: middle
}
ul.expense-box li div h2,
ul.expense-box li span h2 {
    margin-bottom: 0;
    font-weight: 400
}
ul.expense-box li div h4,
ul.expense-box li span h4 {
    margin-top: 0
}
.minus-margin {
    margin: 0 -25px
}
.manage-users {
    margin-bottom: 30px
}
.manage-users .tabs-style-iconbox nav {
    background: #41b3f9
}
.manage-users .tabs-style-iconbox nav ul li a {
    color: rgba(255, 255, 255, .6);
    text-transform: uppercase
}
.manage-users .tabs-style-iconbox nav ul li a.sticon:before {
    margin-bottom: 15px
}
.manage-users .tabs-style-iconbox nav ul li.tab-current a {
    box-shadow: none
}
ul.side-icon-text {
    margin: 0;
    padding: 0
}
ul.side-icon-text>li {
    list-style: none;
    display: inline-block;
    margin-right: 10px
}
ul.side-icon-text>li a {
    color: #313131;
    font-weight: 400
}
ul.side-icon-text>li a:hover {
    color: #41b3f9
}
ul.side-icon-text>li a span {
    margin-right: 10px
}
.manage-table {
    border-top: 1px solid rgba(120, 130, 140, .13);
    margin: 10px -25px 0;
    background: #f7fafc;
    padding: 30px
}
.table tbody tr.advance-table-row {
    border: 2px solid rgba(120, 130, 140, .13);
    white-space: nowrap
}
.table tbody tr.advance-table-row .checkbox {
    margin: 0
}
.table tbody tr.advance-table-row.active {
    border: 2px solid #2cabe3
}
.table tbody tr.advance-table-row td {
    vertical-align: middle!important;
    border: 0!important;
    font-size: 16px;
    background: #fff
}
td.sm-pd {
    padding: 5px 0!important
}
.demo-container .flot-text,
.demo-container .flot-x-axis,
.wallet-widgets #morris-area-chart2 text {
    display: none
}
ul.wallet-list {
    margin: 0;
    padding: 0
}
ul.wallet-list li {
    list-style: none;
    display: block;
    font-size: 18px;
    padding: 20px;
    border-top: 1px solid rgba(120, 130, 140, .13)
}
ul.wallet-list li i {
    font-size: 24px;
    display: inline-block;
    margin-right: 12px;
    vertical-align: middle;
    color: #41b3f9
}
ul.wallet-list li a {
    vertical-align: middle;
    color: #313131
}
ul.wallet-list li a:hover {
    color: #2cabe3
}
@keyframes dasharray-craziness {
    0% {
        stroke-dasharray: 5px
    }
    50% {
        stroke-dasharray: 6px
    }
    100% {
        stroke-dasharray: 7px
    }
}
#ct-bar-chart,
#ct-city-wth,
#ct-daily-sales,
#ct-extra,
#ct-main-bal,
#ct-polar-chart,
#ct-sales,
#ct-visits,
#ct-weather {
    position: relative
}
#ct-extra .ct-series-a .ct-line,
#ct-extra .ct-series-a .ct-point,
#ct-sales .ct-series-a .ct-line,
#ct-sales .ct-series-a .ct-point,
#ct-weather .ct-series-a .ct-line,
#ct-weather .ct-series-a .ct-point {
    stroke: #fff;
    stroke-shadow: 3px 10px 10px #000
}
#ct-extra .ct-series-a .ct-area,
#ct-sales .ct-series-a .ct-area,
#ct-weather .ct-series-a .ct-area {
    fill: none
}
#ct-extra .ct-grid,
#ct-sales .ct-grid,
#ct-weather .ct-grid {
    stroke: rgba(255, 255, 255, .2);
    stroke-dasharray: 0
}
#ct-weather .ct-series-a .ct-line {
    animation: dasharray-craziness 2s infinite
}
.ct-label {
    font-size: 1em
}
#ct-extra .ct-series-a .ct-line,
#ct-extra .ct-series-a .ct-point {
    stroke: #41b3f9;
    animation: dasharray-craziness .5s infinite
}
#ct-extra .ct-grid {
    stroke: rgba(0, 0, 0, .2);
    stroke-dasharray: 2px
}
#ct-bar-chart .ct-series-a .ct-bar {
    stroke: #41b3f9;
    stroke-width: 7px
}
#ct-main-bal .ct-series-a .ct-line,
#ct-main-bal .ct-series-a .ct-point {
    stroke: none;
    fill: #41b3f9;
    fill-opacity: .5
}
#ct-main-bal .ct-series-b .ct-line,
#ct-main-bal .ct-series-b .ct-point {
    stroke: #41b3f9;
    stroke-width: 1px;
    animation: dasharray-craziness 2s infinite;
    opacity: .8
}
#ct-main-bal .ct-series-b .ct-area {
    fill: #41b3f9;
    fill-opacity: .2
}
#ct-visits .ct-series-a .ct-line,
#ct-visits .ct-series-a .ct-point {
    stroke: #98a6ad
}
#ct-visits .ct-series-b .ct-line,
#ct-visits .ct-series-b .ct-point {
    stroke: #41b3f9
}
#ct-visits .ct-series-a .ct-area {
    fill: #98a6ad;
    fill-opacity: .05
}
#ct-visits .ct-series-b .ct-area {
    fill: #41b3f9;
    fill-opacity: .1
}
#ct-visits .ct-line {
    stroke-width: 2px
}
#ct-city-wth .ct-label {
    color: #fff
}
#ct-city-wth .ct-series-a .ct-line,
#ct-city-wth .ct-series-a .ct-point {
    stroke: #41b3f9
}
#ct-city-wth .ct-series-a .ct-area {
    fill: none
}
#ct-polar-chart .ct-series-a .ct-point,
#ct-polar-chart .ct-series-b .ct-point,
#ct-polar-chart .ct-series-c .ct-point,
#ct-polar-chart .ct-series-d .ct-point {
    stroke-width: 3px
}
#ct-polar-chart .ct-series-a .ct-area {
    fill: #41b3f9
}
#ct-polar-chart .ct-series-b .ct-area {
    fill: #7ace4c
}
#ct-polar-chart .ct-series-c .ct-area {
    fill: #f33155
}
#ct-polar-chart .ct-series-d .ct-area {
    fill: #fb4
}
#ct-daily-sales .ct-series-a .ct-bar {
    stroke: rgba(255, 255, 255, .7);
    stroke-width: 10px
}
.dp-table {
    display: table;
    width: 100%;
    margin: 0;
    padding: 0
}
.dp-table li {
    margin: 0;
    padding: 0;
    list-style: none;
    display: table-cell;
    text-align: center
}
.calendar-widget {
    display: block;
    background: #fff;
    overflow: hidden
}
.calendar-widget .cal-left {
    width: 30%;
    float: left;
    position: absolute;
    padding: 5%;
    height: 100%
}
.calendar-widget .cal-left .cal-btm-text {
    position: absolute;
    bottom: 40px;
    font-weight: 400
}
.calendar-widget .cal-left h1 {
    font-size: 50px;
    margin-bottom: 0;
    font-weight: 400
}
.calendar-widget .cal-left span {
    width: 100px;
    border-top: 2px solid #7ace4c;
    height: 2px;
    margin: 3px 0;
    display: inline-block
}
.calendar-widget .cal-right {
    width: 70%;
    float: right;
    min-height: 200px
}
.calendar-widget .cal-right .cal-table {
    width: 100%
}
.calendar-widget .cal-right .cal-table td {
    padding: 18px 15px;
    text-align: center;
    font-weight: 400
}
.calendar-widget .cal-right .cal-table td h1 {
    text-align: left;
    font-weight: 400;
    padding-left: 30px
}
.calendar-widget .cal-right .cal-table td .cal-add {
    font-size: 24px
}
.calendar-widget .cal-right .cal-table td.cal-active {
    border-radius: 60px;
    background: rgba(0, 0, 0, .1)
}
.real-time-widgets {
    text-align: center;
    position: relative
}
.real-time-widgets .data-text {
    width: 200px;
    margin: 0 auto;
    position: absolute;
    left: 0;
    z-index: 200;
    right: 0;
    top: 110px
}
.real-time-widgets .data-text h1 {
    font-size: 50px
}
.real-time-widgets .data-text h5 {
    width: 70px;
    margin: 0 auto 10px;
    padding-bottom: 8px;
    border-bottom: 2px solid #7ace4c
}
.real-time-widgets .data-text span {
    font-size: 18px;
    font-weight: 400
}
.profile-social-icons {
    padding-bottom: 30px;
    font-size: 20px
}
.profile-social-icons a {
    color: #98a6ad
}
.mailbox-widget .customtab {
    border-bottom: 0
}
.mailbox-widget .customtab li a {
    color: #fff
}
.mailbox-widget .customtab li a:hover {
    background: 0 0;
    opacity: .5
}
.mailbox-widget .customtab li.active a,
.mailbox-widget .customtab li.active a:focus {
    background: 0 0;
    color: #fff;
    border-color: #7ace4c
}
.sk-chat-widgets .chatonline {
    padding: 0
}
.sk-chat-widgets .chatonline li {
    list-style: none;
    padding: 5px 0;
    position: relative
}
.sk-chat-widgets .chatonline li a {
    float: none;
    display: inline-block
}
.sk-chat-widgets .chatonline li a img {
    width: 40px
}
.sk-chat-widgets .chatonline li .call-chat {
    position: absolute;
    right: 0;
    display: none;
    top: 20px
}
.sk-chat-widgets .chatonline li:hover .call-chat {
    display: block
}
.chat-box-input {
    border: 0;
    width: 100%;
    height: 60px;
    resize: none;
    line-height: 24px
}
.manage-u-table select {
    max-width: 150px;
    border-radius: 60px
}
.manage-u-table td {
    white-space: nowrap
}
.city-weather-widget .side-icon-text i {
    font-size: 50px;
    margin-right: 15px
}
.city-weather-widget .side-icon-text h1 {
    font-weight: 500
}
.city-weather-days {
    padding: 0 15px
}
.city-weather-days li {
    text-align: center;
    font-size: 16px;
    padding: 18px 0;
    border-left: 1px solid rgba(120, 130, 140, .13);
    border-top: 1px solid rgba(120, 130, 140, .13)
}
.city-weather-days li span {
    display: block;
    text-transform: uppercase;
    line-height: 24px;
    padding: 7px 0
}
.city-weather-days li i {
    font-size: 30px;
    color: #e8e8e8
}
.city-weather-days li.active {
    border-bottom: 2px solid #f33155
}
.city-weather-days li.active i {
    color: #f33155
}
.weather-with-bg .wt-top .wt-img {
    width: 100%;
    height: 350px;
    padding: 40px 60px;
    background-size: cover;
    background-position: center center;
    overflow: hidden
}
.weather-with-bg .wt-top .wt-img h1,
.weather-with-bg .wt-top .wt-img h4,
.weather-with-bg .wt-top .wt-img i {
    color: #fff
}
.weather-with-bg .wt-top .wt-img .side-icon-text li i {
    font-size: 60px;
    margin-right: 20px
}
.weather-with-bg .wt-top .wt-img .side-icon-text li h1 {
    font-size: 60px
}
.weather-with-bg .wt-top .wt-img .wt-city-text {
    padding-top: 50px
}
.weather-with-bg .wt-counter li {
    display: inline-block;
    padding: 10px 7.5px
}
.weather-with-bg .wt-counter li a {
    min-width: 50px;
    display: block;
    padding: 13px;
    height: 50px;
    color: #313131;
    font-size: 17px;
    text-align: center;
    border-radius: 100%
}
.weather-with-bg .wt-counter li.active a {
    background: #2cabe3;
    color: #fff
}
.mt-gauge {
    background: #fff;
    height: 314px
}
.calendar-events {
    padding: 8px 10px;
    border: 1px solid #fff;
    cursor: move
}
.calendar-events:hover {
    border: 1px dashed rgba(120, 130, 140, .13)
}
.calendar-events i {
    margin-right: 8px
}
.earning-box {
    padding: 0;
    margin: 0
}
.earning-box li {
    display: box;
    list-style: none;
    padding: 20px 0
}
.earning-box li .er-row {
    overflow: hidden
}
.earning-box li .er-row .er-pic {
    float: left;
    margin-right: 20px
}
.earning-box li .er-row .er-pic img {
    width: 60px
}
.earning-box li .er-row .er-text {
    float: left;
    width: 45%
}
.earning-box li .er-row .er-text h3 {
    margin: 5px 0 0;
    font-weight: 400;
    font-size: 18px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden
}
.earning-box li .er-row .er-count {
    float: right;
    font-size: 30px;
    padding-top: 5px;
    color: #41b3f9;
    font-weight: 400
}
.todo-list li {
    border: 0;
    border-bottom: 1px solid rgba(120, 130, 140, .13);
    margin-bottom: 0;
    padding: 20px 15px 15px 0
}
.todo-list li .checkbox label {
    font-weight: 400
}
.todo-list li:last-child {
    border-bottom: 0
}
.todo-list li .assignedto {
    padding: 0 0 0 27px;
    margin: 0
}
.todo-list li .assignedto li {
    list-style: none;
    padding: 0;
    display: inline-block;
    border: 0;
    margin-right: 2px
}
.todo-list li .assignedto li img {
    width: 30px;
    border-radius: 100%
}
.todo-list li .item-date {
    padding-left: 25px;
    font-size: 12px;
    display: inline-block
}
.list-task .task-done span {
    text-decoration: line-through
}
.no-bg-addon .input-group-addon {
    background-color: #fff;
    border: 1px solid rgba(120, 130, 140, .13);
    left: -2px;
    position: relative;
    z-index: 10;
    border-left: 0;
    color: #e4e7ea;
    border-radius: 0 3px 3px 0
}
.no-bg-addon .form-control {
    transition: 0s
}
.no-bg-addon .form-control:focus+.input-group-addon {
    border-color: #313131;
    color: #313131
}
.select-mode .btn {
    padding: 15px 0
}
.select-mode .btn.btn-default:focus {
    border-color: #41b3f9;
    color: #fff;
    background: #41b3f9
}
ul.select-row-icon {
    padding: 0;
    margin: 0
}
ul.select-row-icon li {
    display: block;
    list-style: none
}
ul.select-row-icon li a {
    display: block;
    color: #313131;
    padding: 8px 15px;
    position: relative;
    border: 2px solid #fff
}
ul.select-row-icon li a i {
    font-size: 24px;
    vertical-align: middle;
    padding-right: 10px
}
ul.select-row-icon li a i.whn-hov {
    color: #41b3f9;
    display: none;
    float: right;
    position: absolute;
    right: 15px;
    top: 10px
}
ul.select-row-icon li a.selected,
ul.select-row-icon li a:hover {
    border: 2px solid rgba(120, 130, 140, .13)
}
ul.select-row-icon li a.selected i.whn-hov,
ul.select-row-icon li a:hover i.whn-hov {
    display: inline-block
}
.sidebar {
    overflow-y: auto
}
.sidebar .sidebar-nav.navbar-collapse {
    padding-left: 0;
    padding-right: 0
}
.sidebar .fa-fw {
    width: 20px;
    text-align: center!important;
    display: inline-block;
    font-style: normal;
    font-weight: 500;
    margin-right: 7px;
    font-size: 16px;
    vertical-align: middle
}
.sidebar .mdi {
    font-size: 21px
}
.sidebar .sidebar-head {
    padding: 4px 20px;
    width: 240px;
    position: fixed;
    z-index: 10;
    left: 0;
    top: 0
}
.sidebar .sidebar-head h3 {
    color: #fff;
    font-weight: 400
}
.sidebar .sidebar-head h3 i {
    font-size: 20px
}
.sidebar:hover .sidebar-head {
    width: 240px
}
.sidebar .label {
    font-size: 10px;
    border-radius: 60px;
    padding: 6px 8px;
    min-width: 30px;
    height: 20px;
    margin-top: 0
}
.sidebar #side-menu .user-pro .img-circle {
    width: 30px;
    margin-right: 10px
}
.sidebar #side-menu .user-pro>a {
    padding-left: 15px
}
.sidebar #side-menu .user-pro ul li a {
    padding-left: 25px
}
.sidebar #side-menu .user-pro .nav-second-level li i {
    margin-right: 10px
}
#side-menu {
    overflow: hidden
}
.sidebar .sidebar-search {
    padding: 15px
}
#side-menu li.active>a {
    background: rgba(0, 0, 0, 0)
}
#side-menu li a {
    color: #97999f;
    width: 240px
}
#side-menu li a:focus {
    background: rgba(0, 0, 0, 0)
}
#side-menu li.devider {
    margin: 7px 0;
    border-top: 1px solid rgba(120, 130, 140, .13)
}
#side-menu>li>a {
    padding: 15px 35px 15px 20px;
    display: block
}
#side-menu>li>a:focus,
#side-menu>li>a:hover {
    background: rgba(0, 0, 0, .1)
}
#side-menu>li>a.active {
    color: #2cabe3
}
#side-menu ul>li>a:hover {
    color: #2cabe3;
    background: 0 0
}
#side-menu ul>li>a.active {
    color: #2cabe3
}
.sidebar .arrow {
    position: absolute;
    right: 20px;
    top: 17px
}
.sidebar .nav-second-level .arrow {
    right: 20px;
    top: 17px
}
.sidebar .fa.arrow:before {
    content: "\f105"
}
.sidebar .active>a>span>.fa.arrow:before {
    content: "\f107"
}
.sidebar .nav-second-level li,
.sidebar .nav-third-level li {
    border-bottom: none!important
}
.sidebar .nav-second-level li a {
    padding: 14px 10px 14px 40px
}
.sidebar .nav-third-level li a {
    padding-left: 60px
}
.content-wrapper .nicescroll-rails {
    display: none!important
}
/*!
 *  Font Awesome 4.5.0 by @davegandy - http://fontawesome.io - @fontawesome
 *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)
 */

@font-face {
    font-family: FontAwesome;
    /*src: url(../less/icons/font-awesome/fonts/fontawesome-webfont.eot?v=4.5.0);*/
    /*src: url(../less/icons/font-awesome/fonts/fontawesome-webfont.eot?#iefix&v=4.5.0) format('embedded-opentype'), url(../less/icons/font-awesome/fonts/fontawesome-webfont.woff2?v=4.5.0) format('woff2'), url(../less/icons/font-awesome/fonts/fontawesome-webfont.woff?v=4.5.0) format('woff'), url(../less/icons/font-awesome/fonts/fontawesome-webfont.ttf?v=4.5.0) format('truetype'), url(../less/icons/font-awesome/fonts/fontawesome-webfont.svg?v=4.5.0#fontawesomeregular) format('svg');*/
    font-weight: 400;
    font-style: normal
}
.fa {
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}
.fa-lg {
    font-size: 1.33333333em;
    line-height: .75em;
    vertical-align: -15%
}
.fa-2x {
    font-size: 2em
}
.fa-3x {
    font-size: 3em
}
.fa-4x {
    font-size: 4em
}
.fa-5x {
    font-size: 5em
}
.fa-fw {
    text-align: center
}
.fa-ul {
    padding-left: 0;
    margin-left: 2.14285714em;
    list-style-type: none
}
.fa-ul>li {
    position: relative
}
.fa-li {
    position: absolute;
    left: -2.14285714em;
    width: 2.14285714em;
    top: .14285714em;
    text-align: center
}
.fa-li.fa-lg {
    left: -1.85714286em
}
.fa-border {
    padding: .2em .25em .15em;
    border: .08em solid #eee;
    border-radius: .1em
}
.fa-pull-left {
    float: left
}
.fa-pull-right {
    float: right
}
.fa.fa-pull-left {
    margin-right: .3em
}
.fa.fa-pull-right {
    margin-left: .3em
}
.pull-right {
    float: right
}
.pull-left {
    float: left
}
.fa.pull-left {
    margin-right: .3em
}
.fa.pull-right {
    margin-left: .3em
}
.fa-spin {
    -webkit-animation: fa-spin 2s infinite linear;
    animation: fa-spin 2s infinite linear
}
.fa-pulse {
    -webkit-animation: fa-spin 1s infinite steps(8);
    animation: fa-spin 1s infinite steps(8)
}
@-webkit-keyframes fa-spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }
    100% {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg)
    }
}
@keyframes fa-spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }
    100% {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg)
    }
}
.fa-rotate-90 {
    filter: progid: DXImageTransform.Microsoft.BasicImage(rotation=1);
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg)
}
.fa-rotate-180 {
    filter: progid: DXImageTransform.Microsoft.BasicImage(rotation=2);
    -webkit-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg)
}
.fa-rotate-270 {
    filter: progid: DXImageTransform.Microsoft.BasicImage(rotation=3);
    -webkit-transform: rotate(270deg);
    -ms-transform: rotate(270deg);
    transform: rotate(270deg)
}
.fa-flip-horizontal {
    filter: progid: DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1);
    -webkit-transform: scale(-1, 1);
    -ms-transform: scale(-1, 1);
    transform: scale(-1, 1)
}
.fa-flip-vertical {
    filter: progid: DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1);
    -webkit-transform: scale(1, -1);
    -ms-transform: scale(1, -1);
    transform: scale(1, -1)
}
:root .fa-flip-horizontal,
:root .fa-flip-vertical,
:root .fa-rotate-180,
:root .fa-rotate-270,
:root .fa-rotate-90 {
    filter: none
}
.fa-stack {
    position: relative;
    display: inline-block;
    width: 2em;
    height: 2em;
    line-height: 2em;
    vertical-align: middle
}
.fa-stack-1x,
.fa-stack-2x {
    position: absolute;
    left: 0;
    width: 100%;
    text-align: center
}
.fa-stack-1x {
    line-height: inherit
}
.fa-stack-2x {
    font-size: 2em
}
.fa-inverse {
    color: #fff
}
.fa-glass:before {
    content: "\f000"
}
.fa-music:before {
    content: "\f001"
}
.fa-search:before {
    content: "\f002"
}
.fa-envelope-o:before {
    content: "\f003"
}
.fa-heart:before {
    content: "\f004"
}
.fa-star:before {
    content: "\f005"
}
.fa-star-o:before {
    content: "\f006"
}
.fa-user:before {
    content: "\f007"
}
.fa-film:before {
    content: "\f008"
}
.fa-th-large:before {
    content: "\f009"
}
.fa-th:before {
    content: "\f00a"
}
.fa-th-list:before {
    content: "\f00b"
}
.fa-check:before {
    content: "\f00c"
}
.fa-close:before,
.fa-remove:before,
.fa-times:before {
    content: "\f00d"
}
.fa-search-plus:before {
    content: "\f00e"
}
.fa-search-minus:before {
    content: "\f010"
}
.fa-power-off:before {
    content: "\f011"
}
.fa-signal:before {
    content: "\f012"
}
.fa-cog:before,
.fa-gear:before {
    content: "\f013"
}
.fa-trash-o:before {
    content: "\f014"
}
.fa-home:before {
    content: "\f015"
}
.fa-file-o:before {
    content: "\f016"
}
.fa-clock-o:before {
    content: "\f017"
}
.fa-road:before {
    content: "\f018"
}
.fa-download:before {
    content: "\f019"
}
.fa-arrow-circle-o-down:before {
    content: "\f01a"
}
.fa-arrow-circle-o-up:before {
    content: "\f01b"
}
.fa-inbox:before {
    content: "\f01c"
}
.fa-play-circle-o:before {
    content: "\f01d"
}
.fa-repeat:before,
.fa-rotate-right:before {
    content: "\f01e"
}
.fa-refresh:before {
    content: "\f021"
}
.fa-list-alt:before {
    content: "\f022"
}
.fa-lock:before {
    content: "\f023"
}
.fa-flag:before {
    content: "\f024"
}
.fa-headphones:before {
    content: "\f025"
}
.fa-volume-off:before {
    content: "\f026"
}
.fa-volume-down:before {
    content: "\f027"
}
.fa-volume-up:before {
    content: "\f028"
}
.fa-qrcode:before {
    content: "\f029"
}
.fa-barcode:before {
    content: "\f02a"
}
.fa-tag:before {
    content: "\f02b"
}
.fa-tags:before {
    content: "\f02c"
}
.fa-book:before {
    content: "\f02d"
}
.fa-bookmark:before {
    content: "\f02e"
}
.fa-print:before {
    content: "\f02f"
}
.fa-camera:before {
    content: "\f030"
}
.fa-font:before {
    content: "\f031"
}
.fa-bold:before {
    content: "\f032"
}
.fa-italic:before {
    content: "\f033"
}
.fa-text-height:before {
    content: "\f034"
}
.fa-text-width:before {
    content: "\f035"
}
.fa-align-left:before {
    content: "\f036"
}
.fa-align-center:before {
    content: "\f037"
}
.fa-align-right:before {
    content: "\f038"
}
.fa-align-justify:before {
    content: "\f039"
}
.fa-list:before {
    content: "\f03a"
}
.fa-dedent:before,
.fa-outdent:before {
    content: "\f03b"
}
.fa-indent:before {
    content: "\f03c"
}
.fa-video-camera:before {
    content: "\f03d"
}
.fa-image:before,
.fa-photo:before,
.fa-picture-o:before {
    content: "\f03e"
}
.fa-pencil:before {
    content: "\f040"
}
.fa-map-marker:before {
    content: "\f041"
}
.fa-adjust:before {
    content: "\f042"
}
.fa-tint:before {
    content: "\f043"
}
.fa-edit:before,
.fa-pencil-square-o:before {
    content: "\f044"
}
.fa-share-square-o:before {
    content: "\f045"
}
.fa-check-square-o:before {
    content: "\f046"
}
.fa-arrows:before {
    content: "\f047"
}
.fa-step-backward:before {
    content: "\f048"
}
.fa-fast-backward:before {
    content: "\f049"
}
.fa-backward:before {
    content: "\f04a"
}
.fa-play:before {
    content: "\f04b"
}
.fa-pause:before {
    content: "\f04c"
}
.fa-stop:before {
    content: "\f04d"
}
.fa-forward:before {
    content: "\f04e"
}
.fa-fast-forward:before {
    content: "\f050"
}
.fa-step-forward:before {
    content: "\f051"
}
.fa-eject:before {
    content: "\f052"
}
.fa-chevron-left:before {
    content: "\f053"
}
.fa-chevron-right:before {
    content: "\f054"
}
.fa-plus-circle:before {
    content: "\f055"
}
.fa-minus-circle:before {
    content: "\f056"
}
.fa-times-circle:before {
    content: "\f057"
}
.fa-check-circle:before {
    content: "\f058"
}
.fa-question-circle:before {
    content: "\f059"
}
.fa-info-circle:before {
    content: "\f05a"
}
.fa-crosshairs:before {
    content: "\f05b"
}
.fa-times-circle-o:before {
    content: "\f05c"
}
.fa-check-circle-o:before {
    content: "\f05d"
}
.fa-ban:before {
    content: "\f05e"
}
.fa-arrow-left:before {
    content: "\f060"
}
.fa-arrow-right:before {
    content: "\f061"
}
.fa-arrow-up:before {
    content: "\f062"
}
.fa-arrow-down:before {
    content: "\f063"
}
.fa-mail-forward:before,
.fa-share:before {
    content: "\f064"
}
.fa-expand:before {
    content: "\f065"
}
.fa-compress:before {
    content: "\f066"
}
.fa-plus:before {
    content: "\f067"
}
.fa-minus:before {
    content: "\f068"
}
.fa-asterisk:before {
    content: "\f069"
}
.fa-exclamation-circle:before {
    content: "\f06a"
}
.fa-gift:before {
    content: "\f06b"
}
.fa-leaf:before {
    content: "\f06c"
}
.fa-fire:before {
    content: "\f06d"
}
.fa-eye:before {
    content: "\f06e"
}
.fa-eye-slash:before {
    content: "\f070"
}
.fa-exclamation-triangle:before,
.fa-warning:before {
    content: "\f071"
}
.fa-plane:before {
    content: "\f072"
}
.fa-calendar:before {
    content: "\f073"
}
.fa-random:before {
    content: "\f074"
}
.fa-comment:before {
    content: "\f075"
}
.fa-magnet:before {
    content: "\f076"
}
.fa-chevron-up:before {
    content: "\f077"
}
.fa-chevron-down:before {
    content: "\f078"
}
.fa-retweet:before {
    content: "\f079"
}
.fa-shopping-cart:before {
    content: "\f07a"
}
.fa-folder:before {
    content: "\f07b"
}
.fa-folder-open:before {
    content: "\f07c"
}
.fa-arrows-v:before {
    content: "\f07d"
}
.fa-arrows-h:before {
    content: "\f07e"
}
.fa-bar-chart-o:before,
.fa-bar-chart:before {
    content: "\f080"
}
.fa-twitter-square:before {
    content: "\f081"
}
.fa-facebook-square:before {
    content: "\f082"
}
.fa-camera-retro:before {
    content: "\f083"
}
.fa-key:before {
    content: "\f084"
}
.fa-cogs:before,
.fa-gears:before {
    content: "\f085"
}
.fa-comments:before {
    content: "\f086"
}
.fa-thumbs-o-up:before {
    content: "\f087"
}
.fa-thumbs-o-down:before {
    content: "\f088"
}
.fa-star-half:before {
    content: "\f089"
}
.fa-heart-o:before {
    content: "\f08a"
}
.fa-sign-out:before {
    content: "\f08b"
}
.fa-linkedin-square:before {
    content: "\f08c"
}
.fa-thumb-tack:before {
    content: "\f08d"
}
.fa-external-link:before {
    content: "\f08e"
}
.fa-sign-in:before {
    content: "\f090"
}
.fa-trophy:before {
    content: "\f091"
}
.fa-github-square:before {
    content: "\f092"
}
.fa-upload:before {
    content: "\f093"
}
.fa-lemon-o:before {
    content: "\f094"
}
.fa-phone:before {
    content: "\f095"
}
.fa-square-o:before {
    content: "\f096"
}
.fa-bookmark-o:before {
    content: "\f097"
}
.fa-phone-square:before {
    content: "\f098"
}
.fa-twitter:before {
    content: "\f099"
}
.fa-facebook-f:before,
.fa-facebook:before {
    content: "\f09a"
}
.fa-github:before {
    content: "\f09b"
}
.fa-unlock:before {
    content: "\f09c"
}
.fa-credit-card:before {
    content: "\f09d"
}
.fa-feed:before,
.fa-rss:before {
    content: "\f09e"
}
.fa-hdd-o:before {
    content: "\f0a0"
}
.fa-bullhorn:before {
    content: "\f0a1"
}
.fa-bell:before {
    content: "\f0f3"
}
.fa-certificate:before {
    content: "\f0a3"
}
.fa-hand-o-right:before {
    content: "\f0a4"
}
.fa-hand-o-left:before {
    content: "\f0a5"
}
.fa-hand-o-up:before {
    content: "\f0a6"
}
.fa-hand-o-down:before {
    content: "\f0a7"
}
.fa-arrow-circle-left:before {
    content: "\f0a8"
}
.fa-arrow-circle-right:before {
    content: "\f0a9"
}
.fa-arrow-circle-up:before {
    content: "\f0aa"
}
.fa-arrow-circle-down:before {
    content: "\f0ab"
}
.fa-globe:before {
    content: "\f0ac"
}
.fa-wrench:before {
    content: "\f0ad"
}
.fa-tasks:before {
    content: "\f0ae"
}
.fa-filter:before {
    content: "\f0b0"
}
.fa-briefcase:before {
    content: "\f0b1"
}
.fa-arrows-alt:before {
    content: "\f0b2"
}
.fa-group:before,
.fa-users:before {
    content: "\f0c0"
}
.fa-chain:before,
.fa-link:before {
    content: "\f0c1"
}
.fa-cloud:before {
    content: "\f0c2"
}
.fa-flask:before {
    content: "\f0c3"
}
.fa-cut:before,
.fa-scissors:before {
    content: "\f0c4"
}
.fa-copy:before,
.fa-files-o:before {
    content: "\f0c5"
}
.fa-paperclip:before {
    content: "\f0c6"
}
.fa-floppy-o:before,
.fa-save:before {
    content: "\f0c7"
}
.fa-square:before {
    content: "\f0c8"
}
.fa-bars:before,
.fa-navicon:before,
.fa-reorder:before {
    content: "\f0c9"
}
.fa-list-ul:before {
    content: "\f0ca"
}
.fa-list-ol:before {
    content: "\f0cb"
}
.fa-strikethrough:before {
    content: "\f0cc"
}
.fa-underline:before {
    content: "\f0cd"
}
.fa-table:before {
    content: "\f0ce"
}
.fa-magic:before {
    content: "\f0d0"
}
.fa-truck:before {
    content: "\f0d1"
}
.fa-pinterest:before {
    content: "\f0d2"
}
.fa-pinterest-square:before {
    content: "\f0d3"
}
.fa-google-plus-square:before {
    content: "\f0d4"
}
.fa-google-plus:before {
    content: "\f0d5"
}
.fa-money:before {
    content: "\f0d6"
}
.fa-caret-down:before {
    content: "\f0d7"
}
.fa-caret-up:before {
    content: "\f0d8"
}
.fa-caret-left:before {
    content: "\f0d9"
}
.fa-caret-right:before {
    content: "\f0da"
}
.fa-columns:before {
    content: "\f0db"
}
.fa-sort:before,
.fa-unsorted:before {
    content: "\f0dc"
}
.fa-sort-desc:before,
.fa-sort-down:before {
    content: "\f0dd"
}
.fa-sort-asc:before,
.fa-sort-up:before {
    content: "\f0de"
}
.fa-envelope:before {
    content: "\f0e0"
}
.fa-linkedin:before {
    content: "\f0e1"
}
.fa-rotate-left:before,
.fa-undo:before {
    content: "\f0e2"
}
.fa-gavel:before,
.fa-legal:before {
    content: "\f0e3"
}
.fa-dashboard:before,
.fa-tachometer:before {
    content: "\f0e4"
}
.fa-comment-o:before {
    content: "\f0e5"
}
.fa-comments-o:before {
    content: "\f0e6"
}
.fa-bolt:before,
.fa-flash:before {
    content: "\f0e7"
}
.fa-sitemap:before {
    content: "\f0e8"
}
.fa-umbrella:before {
    content: "\f0e9"
}
.fa-clipboard:before,
.fa-paste:before {
    content: "\f0ea"
}
.fa-lightbulb-o:before {
    content: "\f0eb"
}
.fa-exchange:before {
    content: "\f0ec"
}
.fa-cloud-download:before {
    content: "\f0ed"
}
.fa-cloud-upload:before {
    content: "\f0ee"
}
.fa-user-md:before {
    content: "\f0f0"
}
.fa-stethoscope:before {
    content: "\f0f1"
}
.fa-suitcase:before {
    content: "\f0f2"
}
.fa-bell-o:before {
    content: "\f0a2"
}
.fa-coffee:before {
    content: "\f0f4"
}
.fa-cutlery:before {
    content: "\f0f5"
}
.fa-file-text-o:before {
    content: "\f0f6"
}
.fa-building-o:before {
    content: "\f0f7"
}
.fa-hospital-o:before {
    content: "\f0f8"
}
.fa-ambulance:before {
    content: "\f0f9"
}
.fa-medkit:before {
    content: "\f0fa"
}
.fa-fighter-jet:before {
    content: "\f0fb"
}
.fa-beer:before {
    content: "\f0fc"
}
.fa-h-square:before {
    content: "\f0fd"
}
.fa-plus-square:before {
    content: "\f0fe"
}
.fa-angle-double-left:before {
    content: "\f100"
}
.fa-angle-double-right:before {
    content: "\f101"
}
.fa-angle-double-up:before {
    content: "\f102"
}
.fa-angle-double-down:before {
    content: "\f103"
}
.fa-angle-left:before {
    content: "\f104"
}
.fa-angle-right:before {
    content: "\f105"
}
.fa-angle-up:before {
    content: "\f106"
}
.fa-angle-down:before {
    content: "\f107"
}
.fa-desktop:before {
    content: "\f108"
}
.fa-laptop:before {
    content: "\f109"
}
.fa-tablet:before {
    content: "\f10a"
}
.fa-mobile-phone:before,
.fa-mobile:before {
    content: "\f10b"
}
.fa-circle-o:before {
    content: "\f10c"
}
.fa-quote-left:before {
    content: "\f10d"
}
.fa-quote-right:before {
    content: "\f10e"
}
.fa-spinner:before {
    content: "\f110"
}
.fa-circle:before {
    content: "\f111"
}
.fa-mail-reply:before,
.fa-reply:before {
    content: "\f112"
}
.fa-github-alt:before {
    content: "\f113"
}
.fa-folder-o:before {
    content: "\f114"
}
.fa-folder-open-o:before {
    content: "\f115"
}
.fa-smile-o:before {
    content: "\f118"
}
.fa-frown-o:before {
    content: "\f119"
}
.fa-meh-o:before {
    content: "\f11a"
}
.fa-gamepad:before {
    content: "\f11b"
}
.fa-keyboard-o:before {
    content: "\f11c"
}
.fa-flag-o:before {
    content: "\f11d"
}
.fa-flag-checkered:before {
    content: "\f11e"
}
.fa-terminal:before {
    content: "\f120"
}
.fa-code:before {
    content: "\f121"
}
.fa-mail-reply-all:before,
.fa-reply-all:before {
    content: "\f122"
}
.fa-star-half-empty:before,
.fa-star-half-full:before,
.fa-star-half-o:before {
    content: "\f123"
}
.fa-location-arrow:before {
    content: "\f124"
}
.fa-crop:before {
    content: "\f125"
}
.fa-code-fork:before {
    content: "\f126"
}
.fa-chain-broken:before,
.fa-unlink:before {
    content: "\f127"
}
.fa-question:before {
    content: "\f128"
}
.fa-info:before {
    content: "\f129"
}
.fa-exclamation:before {
    content: "\f12a"
}
.fa-superscript:before {
    content: "\f12b"
}
.fa-subscript:before {
    content: "\f12c"
}
.fa-eraser:before {
    content: "\f12d"
}
.fa-puzzle-piece:before {
    content: "\f12e"
}
.fa-microphone:before {
    content: "\f130"
}
.fa-microphone-slash:before {
    content: "\f131"
}
.fa-shield:before {
    content: "\f132"
}
.fa-calendar-o:before {
    content: "\f133"
}
.fa-fire-extinguisher:before {
    content: "\f134"
}
.fa-rocket:before {
    content: "\f135"
}
.fa-maxcdn:before {
    content: "\f136"
}
.fa-chevron-circle-left:before {
    content: "\f137"
}
.fa-chevron-circle-right:before {
    content: "\f138"
}
.fa-chevron-circle-up:before {
    content: "\f139"
}
.fa-chevron-circle-down:before {
    content: "\f13a"
}
.fa-html5:before {
    content: "\f13b"
}
.fa-css3:before {
    content: "\f13c"
}
.fa-anchor:before {
    content: "\f13d"
}
.fa-unlock-alt:before {
    content: "\f13e"
}
.fa-bullseye:before {
    content: "\f140"
}
.fa-ellipsis-h:before {
    content: "\f141"
}
.fa-ellipsis-v:before {
    content: "\f142"
}
.fa-rss-square:before {
    content: "\f143"
}
.fa-play-circle:before {
    content: "\f144"
}
.fa-ticket:before {
    content: "\f145"
}
.fa-minus-square:before {
    content: "\f146"
}
.fa-minus-square-o:before {
    content: "\f147"
}
.fa-level-up:before {
    content: "\f148"
}
.fa-level-down:before {
    content: "\f149"
}
.fa-check-square:before {
    content: "\f14a"
}
.fa-pencil-square:before {
    content: "\f14b"
}
.fa-external-link-square:before {
    content: "\f14c"
}
.fa-share-square:before {
    content: "\f14d"
}
.fa-compass:before {
    content: "\f14e"
}
.fa-caret-square-o-down:before,
.fa-toggle-down:before {
    content: "\f150"
}
.fa-caret-square-o-up:before,
.fa-toggle-up:before {
    content: "\f151"
}
.fa-caret-square-o-right:before,
.fa-toggle-right:before {
    content: "\f152"
}
.fa-eur:before,
.fa-euro:before {
    content: "\f153"
}
.fa-gbp:before {
    content: "\f154"
}
.fa-dollar:before,
.fa-usd:before {
    content: "\f155"
}
.fa-inr:before,
.fa-rupee:before {
    content: "\f156"
}
.fa-cny:before,
.fa-jpy:before,
.fa-rmb:before,
.fa-yen:before {
    content: "\f157"
}
.fa-rouble:before,
.fa-rub:before,
.fa-ruble:before {
    content: "\f158"
}
.fa-krw:before,
.fa-won:before {
    content: "\f159"
}
.fa-bitcoin:before,
.fa-btc:before {
    content: "\f15a"
}
.fa-file:before {
    content: "\f15b"
}
.fa-file-text:before {
    content: "\f15c"
}
.fa-sort-alpha-asc:before {
    content: "\f15d"
}
.fa-sort-alpha-desc:before {
    content: "\f15e"
}
.fa-sort-amount-asc:before {
    content: "\f160"
}
.fa-sort-amount-desc:before {
    content: "\f161"
}
.fa-sort-numeric-asc:before {
    content: "\f162"
}
.fa-sort-numeric-desc:before {
    content: "\f163"
}
.fa-thumbs-up:before {
    content: "\f164"
}
.fa-thumbs-down:before {
    content: "\f165"
}
.fa-youtube-square:before {
    content: "\f166"
}
.fa-youtube:before {
    content: "\f167"
}
.fa-xing:before {
    content: "\f168"
}
.fa-xing-square:before {
    content: "\f169"
}
.fa-youtube-play:before {
    content: "\f16a"
}
.fa-dropbox:before {
    content: "\f16b"
}
.fa-stack-overflow:before {
    content: "\f16c"
}
.fa-instagram:before {
    content: "\f16d"
}
.fa-flickr:before {
    content: "\f16e"
}
.fa-adn:before {
    content: "\f170"
}
.fa-bitbucket:before {
    content: "\f171"
}
.fa-bitbucket-square:before {
    content: "\f172"
}
.fa-tumblr:before {
    content: "\f173"
}
.fa-tumblr-square:before {
    content: "\f174"
}
.fa-long-arrow-down:before {
    content: "\f175"
}
.fa-long-arrow-up:before {
    content: "\f176"
}
.fa-long-arrow-left:before {
    content: "\f177"
}
.fa-long-arrow-right:before {
    content: "\f178"
}
.fa-apple:before {
    content: "\f179"
}
.fa-windows:before {
    content: "\f17a"
}
.fa-android:before {
    content: "\f17b"
}
.fa-linux:before {
    content: "\f17c"
}
.fa-dribbble:before {
    content: "\f17d"
}
.fa-skype:before {
    content: "\f17e"
}
.fa-foursquare:before {
    content: "\f180"
}
.fa-trello:before {
    content: "\f181"
}
.fa-female:before {
    content: "\f182"
}
.fa-male:before {
    content: "\f183"
}
.fa-gittip:before,
.fa-gratipay:before {
    content: "\f184"
}
.fa-sun-o:before {
    content: "\f185"
}
.fa-moon-o:before {
    content: "\f186"
}
.fa-archive:before {
    content: "\f187"
}
.fa-bug:before {
    content: "\f188"
}
.fa-vk:before {
    content: "\f189"
}
.fa-weibo:before {
    content: "\f18a"
}
.fa-renren:before {
    content: "\f18b"
}
.fa-pagelines:before {
    content: "\f18c"
}
.fa-stack-exchange:before {
    content: "\f18d"
}
.fa-arrow-circle-o-right:before {
    content: "\f18e"
}
.fa-arrow-circle-o-left:before {
    content: "\f190"
}
.fa-caret-square-o-left:before,
.fa-toggle-left:before {
    content: "\f191"
}
.fa-dot-circle-o:before {
    content: "\f192"
}
.fa-wheelchair:before {
    content: "\f193"
}
.fa-vimeo-square:before {
    content: "\f194"
}
.fa-try:before,
.fa-turkish-lira:before {
    content: "\f195"
}
.fa-plus-square-o:before {
    content: "\f196"
}
.fa-space-shuttle:before {
    content: "\f197"
}
.fa-slack:before {
    content: "\f198"
}
.fa-envelope-square:before {
    content: "\f199"
}
.fa-wordpress:before {
    content: "\f19a"
}
.fa-openid:before {
    content: "\f19b"
}
.fa-bank:before,
.fa-institution:before,
.fa-university:before {
    content: "\f19c"
}
.fa-graduation-cap:before,
.fa-mortar-board:before {
    content: "\f19d"
}
.fa-yahoo:before {
    content: "\f19e"
}
.fa-google:before {
    content: "\f1a0"
}
.fa-reddit:before {
    content: "\f1a1"
}
.fa-reddit-square:before {
    content: "\f1a2"
}
.fa-stumbleupon-circle:before {
    content: "\f1a3"
}
.fa-stumbleupon:before {
    content: "\f1a4"
}
.fa-delicious:before {
    content: "\f1a5"
}
.fa-digg:before {
    content: "\f1a6"
}
.fa-pied-piper:before {
    content: "\f1a7"
}
.fa-pied-piper-alt:before {
    content: "\f1a8"
}
.fa-drupal:before {
    content: "\f1a9"
}
.fa-joomla:before {
    content: "\f1aa"
}
.fa-language:before {
    content: "\f1ab"
}
.fa-fax:before {
    content: "\f1ac"
}
.fa-building:before {
    content: "\f1ad"
}
.fa-child:before {
    content: "\f1ae"
}
.fa-paw:before {
    content: "\f1b0"
}
.fa-spoon:before {
    content: "\f1b1"
}
.fa-cube:before {
    content: "\f1b2"
}
.fa-cubes:before {
    content: "\f1b3"
}
.fa-behance:before {
    content: "\f1b4"
}
.fa-behance-square:before {
    content: "\f1b5"
}
.fa-steam:before {
    content: "\f1b6"
}
.fa-steam-square:before {
    content: "\f1b7"
}
.fa-recycle:before {
    content: "\f1b8"
}
.fa-automobile:before,
.fa-car:before {
    content: "\f1b9"
}
.fa-cab:before,
.fa-taxi:before {
    content: "\f1ba"
}
.fa-tree:before {
    content: "\f1bb"
}
.fa-spotify:before {
    content: "\f1bc"
}
.fa-deviantart:before {
    content: "\f1bd"
}
.fa-soundcloud:before {
    content: "\f1be"
}
.fa-database:before {
    content: "\f1c0"
}
.fa-file-pdf-o:before {
    content: "\f1c1"
}
.fa-file-word-o:before {
    content: "\f1c2"
}
.fa-file-excel-o:before {
    content: "\f1c3"
}
.fa-file-powerpoint-o:before {
    content: "\f1c4"
}
.fa-file-image-o:before,
.fa-file-photo-o:before,
.fa-file-picture-o:before {
    content: "\f1c5"
}
.fa-file-archive-o:before,
.fa-file-zip-o:before {
    content: "\f1c6"
}
.fa-file-audio-o:before,
.fa-file-sound-o:before {
    content: "\f1c7"
}
.fa-file-movie-o:before,
.fa-file-video-o:before {
    content: "\f1c8"
}
.fa-file-code-o:before {
    content: "\f1c9"
}
.fa-vine:before {
    content: "\f1ca"
}
.fa-codepen:before {
    content: "\f1cb"
}
.fa-jsfiddle:before {
    content: "\f1cc"
}
.fa-life-bouy:before,
.fa-life-buoy:before,
.fa-life-ring:before,
.fa-life-saver:before,
.fa-support:before {
    content: "\f1cd"
}
.fa-circle-o-notch:before {
    content: "\f1ce"
}
.fa-ra:before,
.fa-rebel:before {
    content: "\f1d0"
}
.fa-empire:before,
.fa-ge:before {
    content: "\f1d1"
}
.fa-git-square:before {
    content: "\f1d2"
}
.fa-git:before {
    content: "\f1d3"
}
.fa-hacker-news:before,
.fa-y-combinator-square:before,
.fa-yc-square:before {
    content: "\f1d4"
}
.fa-tencent-weibo:before {
    content: "\f1d5"
}
.fa-qq:before {
    content: "\f1d6"
}
.fa-wechat:before,
.fa-weixin:before {
    content: "\f1d7"
}
.fa-paper-plane:before,
.fa-send:before {
    content: "\f1d8"
}
.fa-paper-plane-o:before,
.fa-send-o:before {
    content: "\f1d9"
}
.fa-history:before {
    content: "\f1da"
}
.fa-circle-thin:before {
    content: "\f1db"
}
.fa-header:before {
    content: "\f1dc"
}
.fa-paragraph:before {
    content: "\f1dd"
}
.fa-sliders:before {
    content: "\f1de"
}
.fa-share-alt:before {
    content: "\f1e0"
}
.fa-share-alt-square:before {
    content: "\f1e1"
}
.fa-bomb:before {
    content: "\f1e2"
}
.fa-futbol-o:before,
.fa-soccer-ball-o:before {
    content: "\f1e3"
}
.fa-tty:before {
    content: "\f1e4"
}
.fa-binoculars:before {
    content: "\f1e5"
}
.fa-plug:before {
    content: "\f1e6"
}
.fa-slideshare:before {
    content: "\f1e7"
}
.fa-twitch:before {
    content: "\f1e8"
}
.fa-yelp:before {
    content: "\f1e9"
}
.fa-newspaper-o:before {
    content: "\f1ea"
}
.fa-wifi:before {
    content: "\f1eb"
}
.fa-calculator:before {
    content: "\f1ec"
}
.fa-paypal:before {
    content: "\f1ed"
}
.fa-google-wallet:before {
    content: "\f1ee"
}
.fa-cc-visa:before {
    content: "\f1f0"
}
.fa-cc-mastercard:before {
    content: "\f1f1"
}
.fa-cc-discover:before {
    content: "\f1f2"
}
.fa-cc-amex:before {
    content: "\f1f3"
}
.fa-cc-paypal:before {
    content: "\f1f4"
}
.fa-cc-stripe:before {
    content: "\f1f5"
}
.fa-bell-slash:before {
    content: "\f1f6"
}
.fa-bell-slash-o:before {
    content: "\f1f7"
}
.fa-trash:before {
    content: "\f1f8"
}
.fa-copyright:before {
    content: "\f1f9"
}
.fa-at:before {
    content: "\f1fa"
}
.fa-eyedropper:before {
    content: "\f1fb"
}
.fa-paint-brush:before {
    content: "\f1fc"
}
.fa-birthday-cake:before {
    content: "\f1fd"
}
.fa-area-chart:before {
    content: "\f1fe"
}
.fa-pie-chart:before {
    content: "\f200"
}
.fa-line-chart:before {
    content: "\f201"
}
.fa-lastfm:before {
    content: "\f202"
}
.fa-lastfm-square:before {
    content: "\f203"
}
.fa-toggle-off:before {
    content: "\f204"
}
.fa-toggle-on:before {
    content: "\f205"
}
.fa-bicycle:before {
    content: "\f206"
}
.fa-bus:before {
    content: "\f207"
}
.fa-ioxhost:before {
    content: "\f208"
}
.fa-angellist:before {
    content: "\f209"
}
.fa-cc:before {
    content: "\f20a"
}
.fa-ils:before,
.fa-shekel:before,
.fa-sheqel:before {
    content: "\f20b"
}
.fa-meanpath:before {
    content: "\f20c"
}
.fa-buysellads:before {
    content: "\f20d"
}
.fa-connectdevelop:before {
    content: "\f20e"
}
.fa-dashcube:before {
    content: "\f210"
}
.fa-forumbee:before {
    content: "\f211"
}
.fa-leanpub:before {
    content: "\f212"
}
.fa-sellsy:before {
    content: "\f213"
}
.fa-shirtsinbulk:before {
    content: "\f214"
}
.fa-simplybuilt:before {
    content: "\f215"
}
.fa-skyatlas:before {
    content: "\f216"
}
.fa-cart-plus:before {
    content: "\f217"
}
.fa-cart-arrow-down:before {
    content: "\f218"
}
.fa-diamond:before {
    content: "\f219"
}
.fa-ship:before {
    content: "\f21a"
}
.fa-user-secret:before {
    content: "\f21b"
}
.fa-motorcycle:before {
    content: "\f21c"
}
.fa-street-view:before {
    content: "\f21d"
}
.fa-heartbeat:before {
    content: "\f21e"
}
.fa-venus:before {
    content: "\f221"
}
.fa-mars:before {
    content: "\f222"
}
.fa-mercury:before {
    content: "\f223"
}
.fa-intersex:before,
.fa-transgender:before {
    content: "\f224"
}
.fa-transgender-alt:before {
    content: "\f225"
}
.fa-venus-double:before {
    content: "\f226"
}
.fa-mars-double:before {
    content: "\f227"
}
.fa-venus-mars:before {
    content: "\f228"
}
.fa-mars-stroke:before {
    content: "\f229"
}
.fa-mars-stroke-v:before {
    content: "\f22a"
}
.fa-mars-stroke-h:before {
    content: "\f22b"
}
.fa-neuter:before {
    content: "\f22c"
}
.fa-genderless:before {
    content: "\f22d"
}
.fa-facebook-official:before {
    content: "\f230"
}
.fa-pinterest-p:before {
    content: "\f231"
}
.fa-whatsapp:before {
    content: "\f232"
}
.fa-server:before {
    content: "\f233"
}
.fa-user-plus:before {
    content: "\f234"
}
.fa-user-times:before {
    content: "\f235"
}
.fa-bed:before,
.fa-hotel:before {
    content: "\f236"
}
.fa-viacoin:before {
    content: "\f237"
}
.fa-train:before {
    content: "\f238"
}
.fa-subway:before {
    content: "\f239"
}
.fa-medium:before {
    content: "\f23a"
}
.fa-y-combinator:before,
.fa-yc:before {
    content: "\f23b"
}
.fa-optin-monster:before {
    content: "\f23c"
}
.fa-opencart:before {
    content: "\f23d"
}
.fa-expeditedssl:before {
    content: "\f23e"
}
.fa-battery-4:before,
.fa-battery-full:before {
    content: "\f240"
}
.fa-battery-3:before,
.fa-battery-three-quarters:before {
    content: "\f241"
}
.fa-battery-2:before,
.fa-battery-half:before {
    content: "\f242"
}
.fa-battery-1:before,
.fa-battery-quarter:before {
    content: "\f243"
}
.fa-battery-0:before,
.fa-battery-empty:before {
    content: "\f244"
}
.fa-mouse-pointer:before {
    content: "\f245"
}
.fa-i-cursor:before {
    content: "\f246"
}
.fa-object-group:before {
    content: "\f247"
}
.fa-object-ungroup:before {
    content: "\f248"
}
.fa-sticky-note:before {
    content: "\f249"
}
.fa-sticky-note-o:before {
    content: "\f24a"
}
.fa-cc-jcb:before {
    content: "\f24b"
}
.fa-cc-diners-club:before {
    content: "\f24c"
}
.fa-clone:before {
    content: "\f24d"
}
.fa-balance-scale:before {
    content: "\f24e"
}
.fa-hourglass-o:before {
    content: "\f250"
}
.fa-hourglass-1:before,
.fa-hourglass-start:before {
    content: "\f251"
}
.fa-hourglass-2:before,
.fa-hourglass-half:before {
    content: "\f252"
}
.fa-hourglass-3:before,
.fa-hourglass-end:before {
    content: "\f253"
}
.fa-hourglass:before {
    content: "\f254"
}
.fa-hand-grab-o:before,
.fa-hand-rock-o:before {
    content: "\f255"
}
.fa-hand-paper-o:before,
.fa-hand-stop-o:before {
    content: "\f256"
}
.fa-hand-scissors-o:before {
    content: "\f257"
}
.fa-hand-lizard-o:before {
    content: "\f258"
}
.fa-hand-spock-o:before {
    content: "\f259"
}
.fa-hand-pointer-o:before {
    content: "\f25a"
}
.fa-hand-peace-o:before {
    content: "\f25b"
}
.fa-trademark:before {
    content: "\f25c"
}
.fa-registered:before {
    content: "\f25d"
}
.fa-creative-commons:before {
    content: "\f25e"
}
.fa-gg:before {
    content: "\f260"
}
.fa-gg-circle:before {
    content: "\f261"
}
.fa-tripadvisor:before {
    content: "\f262"
}
.fa-odnoklassniki:before {
    content: "\f263"
}
.fa-odnoklassniki-square:before {
    content: "\f264"
}
.fa-get-pocket:before {
    content: "\f265"
}
.fa-wikipedia-w:before {
    content: "\f266"
}
.fa-safari:before {
    content: "\f267"
}
.fa-chrome:before {
    content: "\f268"
}
.fa-firefox:before {
    content: "\f269"
}
.fa-opera:before {
    content: "\f26a"
}
.fa-internet-explorer:before {
    content: "\f26b"
}
.fa-television:before,
.fa-tv:before {
    content: "\f26c"
}
.fa-contao:before {
    content: "\f26d"
}
.fa-500px:before {
    content: "\f26e"
}
.fa-amazon:before {
    content: "\f270"
}
.fa-calendar-plus-o:before {
    content: "\f271"
}
.fa-calendar-minus-o:before {
    content: "\f272"
}
.fa-calendar-times-o:before {
    content: "\f273"
}
.fa-calendar-check-o:before {
    content: "\f274"
}
.fa-industry:before {
    content: "\f275"
}
.fa-map-pin:before {
    content: "\f276"
}
.fa-map-signs:before {
    content: "\f277"
}
.fa-map-o:before {
    content: "\f278"
}
.fa-map:before {
    content: "\f279"
}
.fa-commenting:before {
    content: "\f27a"
}
.fa-commenting-o:before {
    content: "\f27b"
}
.fa-houzz:before {
    content: "\f27c"
}
.fa-vimeo:before {
    content: "\f27d"
}
.fa-black-tie:before {
    content: "\f27e"
}
.fa-fonticons:before {
    content: "\f280"
}
.fa-reddit-alien:before {
    content: "\f281"
}
.fa-edge:before {
    content: "\f282"
}
.fa-credit-card-alt:before {
    content: "\f283"
}
.fa-codiepie:before {
    content: "\f284"
}
.fa-modx:before {
    content: "\f285"
}
.fa-fort-awesome:before {
    content: "\f286"
}
.fa-usb:before {
    content: "\f287"
}
.fa-product-hunt:before {
    content: "\f288"
}
.fa-mixcloud:before {
    content: "\f289"
}
.fa-scribd:before {
    content: "\f28a"
}
.fa-pause-circle:before {
    content: "\f28b"
}
.fa-pause-circle-o:before {
    content: "\f28c"
}
.fa-stop-circle:before {
    content: "\f28d"
}
.fa-stop-circle-o:before {
    content: "\f28e"
}
.fa-shopping-bag:before {
    content: "\f290"
}
.fa-shopping-basket:before {
    content: "\f291"
}
.fa-hashtag:before {
    content: "\f292"
}
.fa-bluetooth:before {
    content: "\f293"
}
.fa-bluetooth-b:before {
    content: "\f294"
}
.fa-percent:before {
    content: "\f295"
}
.sttabs {
    position: relative;
    overflow: hidden;
    margin: 0 auto;
    width: 100%;
    font-weight: 300
}
.sticon::before {
    display: inline-block;
    margin: 0 .4em 0 0;
    vertical-align: middle;
    font-size: 20px;
    speak: none;
    -webkit-backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}
.sttabs nav {
    text-align: center
}
.sttabs nav ul {
    position: relative;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: flex;
    margin: 0 auto;
    padding: 0;
    font-family: Poppins, sans-serif;
    list-style: none;
    -ms-box-orient: horizontal;
    -ms-box-pack: center;
    -webkit-flex-flow: row wrap;
    -moz-flex-flow: row wrap;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    -webkit-justify-content: center;
    -moz-justify-content: center;
    -ms-justify-content: center;
    justify-content: center
}
.sttabs nav ul li {
    position: relative;
    z-index: 1;
    display: block;
    margin: 0;
    text-align: center;
    -webkit-flex: 1;
    -moz-flex: 1;
    -ms-flex: 1;
    flex: 1
}
.sttabs nav a {
    position: relative;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 2.5
}
.sttabs nav a span {
    vertical-align: middle;
    font-wight: 500;
    font-size: 14px;
    font-family: Rubik, sans-serif
}
.sttabs nav a:focus {
    outline: 0
}
.sttabs nav li.tab-current a {
    color: #f33155
}
.content-wrap {
    background: #fff
}
.tabs-style-bar nav ul li a {
    margin: 0 2px;
    background-color: #f7fafc;
    color: #686868;
    padding: 5px 0;
    transition: background-color .2s, color .2s
}
.tabs-style-bar nav ul li a:focus,
.tabs-style-bar nav ul li a:hover {
    color: #f33155
}
.tabs-style-bar nav ul li a span {
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 14px;
    font-family: Poppins, sans-serif
}
.tabs-style-bar nav ul li.tab-current a {
    background: #fb9678;
    color: #fff
}
.tabs-style-iconbox nav {
    background: #f7fafc
}
.tabs-style-iconbox nav ul li a {
    overflow: visible;
    padding: 25px 0;
    line-height: 1;
    -webkit-transition: color .2s;
    transition: color .2s;
    color: #263238
}
.tabs-style-iconbox nav ul li.tab-current {
    z-index: 1
}
.tabs-style-iconbox nav ul li.tab-current a {
    background: #41b3f9;
    color: #fff;
    box-shadow: -1px 0 0 #fff
}
.tabs-style-iconbox nav ul li.tab-current a::after {
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -10px;
    width: 0;
    height: 0;
    border: solid transparent;
    border-width: 10px;
    border-top-color: #41b3f9;
    content: '';
    pointer-events: none
}
.tabs-style-iconbox nav ul li::after,
.tabs-style-iconbox nav ul li:first-child::before {
    position: absolute;
    top: 20%;
    right: 0;
    z-index: -1;
    width: 1px;
    height: 60%;
    content: ''
}
.tabs-style-iconbox nav ul li:first-child::before {
    right: auto;
    left: 0
}
.tabs-style-iconbox .sticon::before {
    display: block;
    margin: 0 0 .25em
}
.tabs-style-underline nav {
    border: 1px solid rgba(120, 130, 140, .13)
}
.tabs-style-underline nav a {
    padding: 20px 0;
    border-left: 1px solid rgba(120, 130, 140, .13);
    -webkit-transition: color .2s;
    transition: color .2s;
    color: #263238
}
.tabs-style-underline nav li:last-child a {
    border-right: 1px solid rgba(120, 130, 140, .13)
}
.tabs-style-underline nav li a::after {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 6px;
    background: #fb9678;
    content: '';
    -webkit-transition: -webkit-transform .3s;
    transition: transform .3s;
    -webkit-transform: translate3d(0, 150%, 0);
    transform: translate3d(0, 150%, 0)
}
.tabs-style-underline nav li.tab-current a::after {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}
.tabs-style-linetriangle nav a {
    overflow: visible;
    border-bottom: 1px solid rgba(0, 0, 0, .2);
    -webkit-transition: color .2s;
    transition: color .2s
}
.tabs-style-linetriangle nav a span {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
    padding: 15px 0;
    color: #263238
}
.tabs-style-linetriangle nav li.tab-current a:after,
.tabs-style-linetriangle nav li.tab-current a:before {
    position: absolute;
    top: 100%;
    left: 50%;
    width: 0;
    height: 0;
    border: solid transparent;
    content: '';
    pointer-events: none
}
.tabs-style-linetriangle nav li.tab-current a:after {
    margin-left: -10px;
    border-width: 10px;
    border-top-color: #fff
}
.tabs-style-linetriangle nav li.tab-current a span {
    color: #f33155
}
.tabs-style-linetriangle nav li.tab-current a:before {
    margin-left: -11px;
    border-width: 11px;
    border-top-color: rgba(0, 0, 0, .2)
}
.tabs-style-iconfall {
    overflow: visible
}
.tabs-style-iconfall nav {
    max-width: 1200px;
    margin: 0 auto
}
.tabs-style-iconfall nav a {
    display: inline-block;
    overflow: visible;
    padding: 1em 0 2em;
    color: #263238;
    line-height: 1;
    -webkit-transition: color .3s cubic-bezier(.7, 0, .3, 1);
    transition: color .3s cubic-bezier(.7, 0, .3, 1)
}
.tabs-style-iconfall nav a:focus,
.tabs-style-iconfall nav a:hover,
.tabs-style-iconfall nav li.tab-current a {
    color: #f33155
}
.tabs-style-iconfall nav li::before {
    position: absolute;
    bottom: 1em;
    left: 50%;
    margin-left: -20px;
    width: 40px;
    height: 4px;
    background: #f33155;
    content: '';
    opacity: 0;
    -webkit-transition: -webkit-transform .2s ease-in;
    transition: transform .2s ease-in;
    -webkit-transform: scale3d(0, 1, 1);
    transform: scale3d(0, 1, 1)
}
.tabs-style-iconfall nav li.tab-current::before {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1)
}
.tabs-style-iconfall nav li.tab-current .sticon::before {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}
.tabs-style-iconfall .sticon::before {
    display: block;
    margin: 0 0 .35em;
    opacity: 0;
    font-size: 24px;
    -webkit-transition: -webkit-transform .2s, opacity .2s;
    transition: transform .2s, opacity .2s;
    -webkit-transform: translate3d(0, -100px, 0);
    transform: translate3d(0, -100px, 0);
    pointer-events: none
}
@media screen and (max-width: 58em) {
    .tabs-style-iconfall nav li .sticon::before {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}
.tabs-style-linemove nav {
    background: #f7fafc
}
.tabs-style-linemove nav li:last-child::before {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: #f33155;
    content: '';
    -webkit-transition: -webkit-transform .3s;
    transition: transform .3s
}
.tabs-style-linemove nav li:first-child.tab-current~li:last-child::before {
    -webkit-transform: translate3d(-400%, 0, 0);
    transform: translate3d(-400%, 0, 0)
}
.tabs-style-linemove nav li:nth-child(2).tab-current~li:last-child::before {
    -webkit-transform: translate3d(-300%, 0, 0);
    transform: translate3d(-300%, 0, 0)
}
.tabs-style-linemove nav li:nth-child(3).tab-current~li:last-child::before {
    -webkit-transform: translate3d(-200%, 0, 0);
    transform: translate3d(-200%, 0, 0)
}
.tabs-style-linemove nav li:nth-child(4).tab-current~li:last-child::before {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0)
}
.tabs-style-linemove nav a {
    padding: 30px 0;
    color: #263238;
    line-height: 1;
    -webkit-transition: color .3s, -webkit-transform .3s;
    transition: color .3s, transform .3s
}
.tabs-style-linemove nav li.tab-current a {
    color: #f33155
}
.tabs-style-line nav a {
    padding: 20px 10px;
    box-shadow: inset 0 -2px #d1d3d2;
    color: #686868;
    text-align: left;
    text-transform: uppercase;
    letter-spacing: 1px;
    line-height: 1;
    -webkit-transition: color .3s, box-shadow .3s;
    transition: color .3s, box-shadow .3s
}
.tabs-style-line nav a:focus,
.tabs-style-line nav a:hover {
    box-shadow: inset 0 -2px #74777b
}
.tabs-style-line nav li.tab-current a {
    box-shadow: inset 0 -2px #f33155;
    color: #f33155
}
@media screen and (max-width: 58em) {
    .tabs-style-line nav ul {
        display: block;
        box-shadow: none
    }
    .tabs-style-line nav ul li {
        display: block;
        -webkit-flex: none;
        flex: none
    }
}
.tabs-style-circle {
    overflow: visible
}
.tabs-style-circle nav li {
    margin-top: 60px!important;
    margin-bottom: 60px!important
}
.tabs-style-circle nav li::before {
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -60px 0 0 -60px;
    width: 120px;
    height: 120px;
    border: 1px solid #fb9678;
    border-radius: 50%;
    content: '';
    opacity: 0;
    -webkit-transition: -webkit-transform .2s, opacity .2s;
    transition: transform .2s, opacity .2s;
    -webkit-transition-timing-function: cubic-bezier(.7, 0, .3, 1);
    transition-timing-function: cubic-bezier(.7, 0, .3, 1)
}
.tabs-style-circle nav a {
    overflow: visible;
    color: #2b2b2b;
    font-weight: 500;
    font-size: 14;
    line-height: 1.1;
    -webkit-transition: color .3s cubic-bezier(.7, 0, .3, 1);
    transition: color .3s cubic-bezier(.7, 0, .3, 1)
}
.tabs-style-circle nav a span {
    display: inline-block
}
.tabs-style-circle nav a:focus,
.tabs-style-circle nav a:hover,
.tabs-style-circle nav li.tab-current a {
    color: #f33155
}
.tabs-style-circle nav li.tab-current a span {
    -webkit-transform: translate3d(0, 4px, 0);
    transform: translate3d(0, 4px, 0)
}
@media screen and (max-width: 58em) {
    .tabs-style-circle nav li::before {
        margin: -40px 0 0 -40px;
        width: 80px;
        height: 80px
    }
}
.tabs-style-circle nav li.tab-current::before {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1)
}
.tabs-style-circle .icon::before,
.tabs-style-circle nav a span {
    -webkit-transition: -webkit-transform .3s cubic-bezier(.7, 0, .3, 1);
    transition: transform .3s cubic-bezier(.7, 0, .3, 1)
}
.tabs-style-circle .sticon::before {
    display: block;
    margin: 0;
    pointer-events: none
}
.tabs-style-circle nav li.tab-current .sticon::before {
    -webkit-transform: translate3d(0, -4px, 0);
    transform: translate3d(0, -4px, 0)
}
.tabs-style-shape {
    max-width: 1200px;
    margin: 0 auto
}
.tabs-style-shape nav ul li {
    margin: 0 3em
}
.tabs-style-shape nav ul li:first-child {
    margin-left: 0
}
.tabs-style-shape nav ul li.tab-current {
    z-index: 1
}
.tabs-style-shape nav li a {
    overflow: visible;
    margin: 0 -3em 0 0;
    padding: 0;
    color: #fff;
    font-weight: 500
}
.tabs-style-shape nav li a svg {
    position: absolute;
    left: 100%;
    margin: 0;
    width: 3em;
    height: 100%;
    fill: #bdc2c9
}
.tabs-style-shape nav li:first-child a span {
    padding-left: 2em;
    border-radius: 30px 0 0
}
.tabs-style-shape nav li:last-child a span {
    padding-right: 2em;
    border-radius: 0 30px 0 0
}
.tabs-style-shape nav li a svg:nth-child(2),
.tabs-style-shape nav li:last-child a svg {
    right: 100%;
    left: auto;
    -webkit-transform: scale3d(-1, 1, 1);
    transform: scale3d(-1, 1, 1)
}
.tabs-style-shape nav li a span {
    display: block;
    overflow: hidden;
    padding: .65em 0;
    background-color: #bdc2c9;
    text-overflow: ellipsis;
    white-space: nowrap
}
.tabs-style-shape nav li a:hover span {
    background-color: #f33155
}
.tabs-style-shape nav li a:hover svg {
    fill: #f33155
}
.tabs-style-shape nav li a svg {
    pointer-events: none
}
.tabs-style-shape nav li a svg use {
    pointer-events: auto
}
.tabs-style-shape nav li.tab-current a span,
.tabs-style-shape nav li.tab-current a svg {
    -webkit-transition: none;
    transition: none
}
.tabs-style-shape nav li.tab-current a span {
    background: #f7fafc
}
.tabs-style-shape nav li.tab-current a svg {
    fill: #f7fafc
}
.tabs-style-shape .content-wrap {
    background: #f7fafc
}
@media screen and (max-width: 58em) {
    .tabs-style-shape nav ul {
        display: block;
        padding-top: 1.5em
    }
    .tabs-style-shape nav ul li {
        display: block;
        margin: -1.25em 0 0;
        -webkit-flex: none;
        flex: none
    }
    .tabs-style-shape nav ul li a {
        margin: 0
    }
    .tabs-style-shape nav ul li svg {
        display: none
    }
    .tabs-style-shape nav ul li a span {
        padding: 1.25em 0 2em!important;
        border-radius: 30px 30px 0 0!important;
        box-shadow: 0 -1px 2px rgba(0, 0, 0, .1);
        line-height: 1
    }
    .tabs-style-shape nav ul li:last-child a span {
        padding: 1.25em 0!important
    }
    .tabs-style-shape nav ul li.tab-current {
        z-index: 1
    }
}
.tabs-style-linebox nav ul li {
    margin: 0 .5em;
    -webkit-flex: none;
    flex: none
}
.tabs-style-linebox nav a {
    padding: 0 1.5em;
    color: #263238;
    font-weight: 500;
    -webkit-transition: color .3s;
    transition: color .3s
}
.tabs-style-linebox nav a:focus,
.tabs-style-linebox nav a:hover {
    color: #f33155
}
.tabs-style-linebox nav li.tab-current a {
    color: #fff
}
.tabs-style-linebox nav a::after {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    background: #d2d8d6;
    content: '';
    -webkit-transition: background-color .3s, -webkit-transform .3s;
    transition: background-color .3s, transform .3s;
    -webkit-transition-timing-function: ease, cubic-bezier(.7, 0, .3, 1);
    transition-timing-function: ease, cubic-bezier(.7, 0, .3, 1);
    -webkit-transform: translate3d(0, 100%, 0) translate3d(0, -3px, 0);
    transform: translate3d(0, 100%, 0) translate3d(0, -3px, 0)
}
.tabs-style-linebox nav li.tab-current a::after {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}
.tabs-style-linebox nav a:focus::after,
.tabs-style-linebox nav a:hover::after,
.tabs-style-linebox nav li.tab-current a::after {
    background: #f33155
}
@media screen and (max-width: 58em) {
    .tabs-style-linebox nav ul {
        display: block;
        box-shadow: none
    }
    .tabs-style-linebox nav ul li {
        display: block;
        -webkit-flex: none;
        flex: none
    }
}
.tabs-style-flip {
    max-width: 1200px;
    margin: 0 auto
}
.tabs-style-flip nav a {
    padding: .5em 0;
    color: #263238;
    -webkit-transition: color .3s;
    transition: color .3s
}
.tabs-style-flip nav a:focus,
.tabs-style-flip nav a:hover {
    color: #f33155
}
.tabs-style-flip nav a span {
    text-transform: uppercase;
    letter-spacing: 1px
}
.tabs-style-flip nav a::after {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    background-color: #f0f0f0;
    content: '';
    -webkit-transition: -webkit-transform .3s, background-color .3s;
    transition: transform .3s, background-color .3s;
    -webkit-transform: perspective(900px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(900px) rotate3d(1, 0, 0, 90deg);
    -webkit-transform-origin: 50% 100%;
    transform-origin: 50% 100%;
    -webkit-perspective-origin: 50% 100%;
    perspective-origin: 50% 100%
}
.tabs-style-flip nav li.tab-current a {
    color: #f33155
}
.tabs-style-flip nav li.tab-current a::after {
    background-color: #f7fafc;
    -webkit-transform: perspective(900px) rotate3d(1, 0, 0, 0deg);
    transform: perspective(900px) rotate3d(1, 0, 0, 0deg)
}
.tabs-style-flip .content-wrap {
    background: #f7fafc
}
.tabs-style-circlefill {
    max-width: 800px;
    border: 1px solid #f33155;
    margin: 0 auto
}
.tabs-style-circlefill nav ul li {
    overflow: hidden;
    border-right: 1px solid #f33155
}
.tabs-style-circlefill nav li a {
    padding: 1.5em 0;
    color: #fff;
    font-size: 1.25em
}
.tabs-style-circlefill nav li:first-child {
    border-left: none
}
.tabs-style-circlefill nav li:last-child {
    border: none
}
.tabs-style-circlefill nav li::before {
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -40px 0 0 -40px;
    width: 80px;
    height: 80px;
    border: 1px solid #f33155;
    border-radius: 50%;
    background: #f33155;
    content: '';
    -webkit-transition: -webkit-transform .3s;
    transition: transform .3s
}
.tabs-style-circlefill nav li.tab-current::before {
    -webkit-transform: scale3d(2.5, 2.5, 1);
    transform: scale3d(2.5, 2.5, 1)
}
.tabs-style-circlefill nav a {
    -webkit-transition: color .3s;
    transition: color .3s
}
.tabs-style-circlefill nav a span {
    display: none
}
.tabs-style-circlefill nav li.tab-current a {
    color: #fff
}
.tabs-style-circlefill .icon::before {
    display: block;
    margin: 0;
    pointer-events: none
}
.tabs-style-circlefill .content-wrap {
    border-top: 1px solid #f33155
}
.content-wrap {
    position: relative
}
.content-wrap section {
    display: none;
    margin: 0 auto;
    padding: 25px;
    min-height: 150px
}
.content-wrap section p {
    margin: 0;
    padding: .75em 0
}
.content-wrap section.content-current {
    display: block
}
.no-js .content-wrap section {
    display: block;
    padding-bottom: 2em;
    border-bottom: 1px solid rgba(255, 255, 255, .6)
}
.no-flexbox nav ul {
    display: block
}
.no-flexbox nav ul li {
    min-width: 15%;
    display: inline-block
}
@media screen and (max-width: 58em) {
    .sttabs nav a span {
        display: none
    }
    .sttabs nav a:before {
        margin-right: 0
    }
}
.mytooltip {
    display: inline;
    position: relative;
    z-index: 9999
}
.tooltip-item {
    background: rgba(0, 0, 0, .1);
    cursor: pointer;
    display: inline-block;
    font-weight: 500;
    padding: 0 10px
}
.tooltip-item::after {
    content: '';
    position: absolute;
    width: 360px;
    height: 20px;
    bottom: 100%;
    left: 50%;
    pointer-events: none;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%)
}
.mytooltip:hover .tooltip-item::after {
    pointer-events: auto
}
.tooltip-content {
    position: absolute;
    z-index: 9999;
    width: 360px;
    left: 50%;
    margin: 0 0 20px -180px;
    bottom: 100%;
    text-align: left;
    font-size: 14px;
    line-height: 30px;
    box-shadow: -5px -5px 15px rgba(48, 54, 61, .2);
    background: #2b2b2b;
    opacity: 0;
    cursor: default;
    pointer-events: none
}
.tooltip-effect-1 .tooltip-content {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0);
    -webkit-transition: opacity .3s, -webkit-transform .3s;
    transition: opacity .3s, transform .3s;
    color: #fff
}
.tooltip-effect-2 .tooltip-content {
    -webkit-transform-origin: 50% calc(110%);
    transform-origin: 50% calc(110%);
    -webkit-transform: perspective(1000px) rotate3d(1, 0, 0, 45deg);
    transform: perspective(1000px) rotate3d(1, 0, 0, 45deg);
    -webkit-transition: opacity .2s, -webkit-transform .2s;
    transition: opacity .2s, transform .2s
}
.tooltip-effect-3 .tooltip-content {
    -webkit-transform: translate3d(0, 10px, 0) rotate3d(1, 1, 0, 25deg);
    transform: translate3d(0, 10px, 0) rotate3d(1, 1, 0, 25deg);
    -webkit-transition: opacity .3s, -webkit-transform .3s;
    transition: opacity .3s, transform .3s
}
.tooltip-effect-4 .tooltip-content {
    -webkit-transform-origin: 50% 100%;
    transform-origin: 50% 100%;
    -webkit-transform: scale3d(.7, .3, 1);
    transform: scale3d(.7, .3, 1);
    -webkit-transition: opacity .2s, -webkit-transform .2s;
    transition: opacity .2s, transform .2s
}
.tooltip-effect-5 .tooltip-content {
    width: 180px;
    margin-left: -90px;
    -webkit-transform-origin: 50% calc(106%);
    transform-origin: 50% calc(106%);
    -webkit-transform: rotate3d(0, 0, 1, 15deg);
    transform: rotate3d(0, 0, 1, 15deg);
    -webkit-transition: opacity .2s, -webkit-transform .2s;
    transition: opacity .2s, transform .2s;
    -webkit-transition-timing-function: ease, cubic-bezier(.17, .67, .4, 1.39);
    transition-timing-function: ease, cubic-bezier(.17, .67, .4, 1.39)
}
.mytooltip:hover .tooltip-content {
    pointer-events: auto;
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 0, 0);
    transform: translate3d(0, 0, 0) rotate3d(0, 0, 0, 0)
}
.tooltip.tooltip-effect-2:hover .tooltip-content {
    -webkit-transform: perspective(1000px) rotate3d(1, 0, 0, 0deg);
    transform: perspective(1000px) rotate3d(1, 0, 0, 0deg)
}
.tooltip-content::after {
    content: '';
    top: 100%;
    left: 50%;
    border: solid transparent;
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: #2a3035 transparent transparent;
    border-width: 10px;
    margin-left: -10px
}
.tooltip-content img {
    position: relative;
    height: 140px;
    display: block;
    float: left;
    margin-right: 1em
}
.tooltip-text {
    font-size: 14px;
    line-height: 24px;
    display: block;
    padding: 1.31em 1.21em 1.21em 0;
    color: #fff
}
.tooltip-effect-5 .tooltip-text {
    padding: 1.4em
}
a.mytooltip {
    font-weight: 500;
    color: #fb9678
}
.tooltip-content2 {
    position: absolute;
    z-index: 9999;
    width: 80px;
    height: 80px;
    padding-top: 25px;
    left: 50%;
    margin-left: -40px;
    bottom: 100%;
    border-radius: 50%;
    text-align: center;
    background: #fb9678;
    color: #fff;
    opacity: 0;
    margin-bottom: 20px;
    cursor: default;
    pointer-events: none
}
.tooltip-content2 i {
    opacity: 0
}
.mytooltip:hover .tooltip-content2,
.mytooltip:hover .tooltip-content2 i {
    opacity: 1;
    font-size: 18px
}
.tooltip-effect-6 .tooltip-content2 {
    -webkit-transform: translate3d(0, 10px, 0) rotate3d(1, 1, 1, 45deg);
    transform: translate3d(0, 10px, 0) rotate3d(1, 1, 1, 45deg);
    -webkit-transform-origin: 50% 100%;
    transform-origin: 50% 100%;
    -webkit-transition: opacity .3s, -webkit-transform .3s;
    transition: opacity .3s, transform .3s
}
.tooltip-effect-6 .tooltip-content2 i {
    -webkit-transform: scale3d(0, 0, 1);
    transform: scale3d(0, 0, 1);
    -webkit-transition: opacity .3s, -webkit-transform .3s;
    transition: opacity .3s, transform .3s
}
.tooltip-effect-7 .tooltip-content2 {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0);
    -webkit-transition: opacity .3s, -webkit-transform .3s;
    transition: opacity .3s, transform .3s
}
.tooltip-effect-7 .tooltip-content2 i {
    -webkit-transform: translate3d(0, 15px, 0);
    transform: translate3d(0, 15px, 0);
    -webkit-transition: opacity .3s, -webkit-transform .3s;
    transition: opacity .3s, transform .3s
}
.tooltip-effect-8 .tooltip-content2 {
    -webkit-transform: translate3d(0, 10px, 0) rotate3d(0, 1, 0, 90deg);
    transform: translate3d(0, 10px, 0) rotate3d(0, 1, 0, 90deg);
    -webkit-transform-origin: 50% 100%;
    transform-origin: 50% 100%;
    -webkit-transition: opacity .3s, -webkit-transform .3s;
    transition: opacity .3s, transform .3s
}
.tooltip-effect-8 .tooltip-content2 i {
    -webkit-transform: scale3d(0, 0, 1);
    transform: scale3d(0, 0, 1);
    -webkit-transition: opacity .3s, -webkit-transform .3s;
    transition: opacity .3s, transform .3s
}
.tooltip-effect-9 .tooltip-content2 {
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0);
    -webkit-transition: opacity .3s, -webkit-transform .3s;
    transition: opacity .3s, transform .3s
}
.tooltip-effect-9 .tooltip-content2 i {
    -webkit-transform: translate3d(0, 20px, 0);
    transform: translate3d(0, 20px, 0);
    -webkit-transition: opacity .3s, -webkit-transform .3s;
    transition: opacity .3s, transform .3s
}
.mytooltip:hover .tooltip-content2,
.mytooltip:hover .tooltip-content2 i {
    pointer-events: auto;
    -webkit-transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
    transform: translate3d(0, 0, 0) scale3d(1, 1, 1)
}
.tooltip-effect-6:hover .tooltip-content2 i {
    -webkit-transform: rotate3d(1, 1, 1, 0);
    transform: rotate3d(1, 1, 1, 0)
}
.tooltip-content2::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin: -7px 0 0 -15px;
    width: 30px;
    height: 20px;
    /*background: url(../../plugins/images/tooltip/tooltip1.svg) center center no-repeat;*/
    background-size: 100%
}
.tooltip-content3 {
    position: absolute;
    /*background: url(../../plugins/images/tooltip/shape1.svg) center bottom no-repeat;*/
    background-size: 100% 100%;
    z-index: 9999;
    width: 200px;
    bottom: 100%;
    left: 50%;
    margin-left: -100px;
    padding: 50px 30px;
    text-align: center;
    color: #fff;
    opacity: 0;
    cursor: default;
    font-size: 14;
    line-height: 27px;
    pointer-events: none;
    -webkit-transform: scale3d(.1, .2, 1);
    transform: scale3d(.1, .2, 1);
    -webkit-transform-origin: 50% 120%;
    transform-origin: 50% 120%;
    -webkit-transition: opacity .4s, -webkit-transform .4s;
    transition: opacity .4s, transform .4s;
    -webkit-transition-timing-function: ease, cubic-bezier(.6, 0, .4, 1);
    transition-timing-function: ease, cubic-bezier(.6, 0, .4, 1)
}
.mytooltip:hover .tooltip-content3 {
    opacity: 1;
    pointer-events: auto;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1)
}
.tooltip-content3::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    left: 50%;
    margin-left: -8px;
    top: 100%;
    background: #00AEEF;
    -webkit-transform: translate3d(0, -60%, 0) rotate3d(0, 0, 1, 45deg);
    transform: translate3d(0, -60%, 0) rotate3d(0, 0, 1, 45deg)
}
.tooltip-item2 {
    color: #03a9f3;
    cursor: pointer;
    z-index: 100;
    position: relative;
    display: inline-block;
    font-weight: 500;
    -webkit-transition: background-color .3s, color .3s, -webkit-transform .3s;
    transition: background-color .3s, color .3s, transform .3s
}
.mytooltip:hover .tooltip-item2 {
    color: #fff;
    -webkit-transform: translate3d(0, -.5em, 0);
    transform: translate3d(0, -.5em, 0)
}
.tooltip-content4 {
    position: absolute;
    z-index: 99;
    width: 360px;
    left: 50%;
    margin-left: -180px;
    bottom: -5px;
    text-align: left;
    background: #03a9f3;
    opacity: 0;
    font-size: 14px;
    line-height: 27px;
    padding: 1.5em;
    color: #fff;
    border-bottom: 55px solid #2b2b2b;
    cursor: default;
    pointer-events: none;
    border-radius: 5px;
    -webkit-transform: translate3d(0, -.5em, 0);
    transform: translate3d(0, -.5em, 0);
    -webkit-transition: opacity .3s, -webkit-transform .3s;
    transition: opacity .3s, transform .3s
}
.tooltip-content4 a {
    color: #2b2b2b
}
.tooltip-text2 {
    opacity: 0;
    -webkit-transform: translate3d(0, 1.5em, 0);
    transform: translate3d(0, 1.5em, 0);
    -webkit-transition: opacity .3s, -webkit-transform .3s;
    transition: opacity .3s, transform .3s
}
.mytooltip:hover .tooltip-content4,
.mytooltip:hover .tooltip-text2 {
    pointer-events: auto;
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}
.tooltip-content5 {
    position: absolute;
    z-index: 9999;
    width: 300px;
    left: 50%;
    bottom: 100%;
    font-size: 20px;
    line-height: 1.4;
    text-align: center;
    font-weight: 400;
    color: #fff;
    background: 0 0;
    opacity: 0;
    margin: 0 0 20px -150px;
    cursor: default;
    pointer-events: none;
    -webkit-font-smoothing: antialiased;
    -webkit-transition: opacity .3s .3s;
    transition: opacity .3s .3s
}
.mytooltip:hover .tooltip-content5 {
    opacity: 1;
    pointer-events: auto;
    -webkit-transition-delay: 0s;
    transition-delay: 0s
}
.tooltip-content5 span {
    display: block
}
.tooltip-text3 {
    border-bottom: 10px solid #fb9678;
    overflow: hidden;
    -webkit-transform: scale3d(0, 1, 1);
    transform: scale3d(0, 1, 1);
    -webkit-transition: -webkit-transform .3s .3s;
    transition: transform .3s .3s
}
.mytooltip:hover .tooltip-text3 {
    -webkit-transition-delay: 0s;
    transition-delay: 0s;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1)
}
.tooltip-inner2 {
    background: #2b2b2b;
    padding: 40px;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    webkit-transition: -webkit-transform .3s;
    transition: transform .3s
}
.mytooltip:hover .tooltip-inner2 {
    -webkit-transition-delay: .3s;
    transition-delay: .3s;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}
.tooltip-content5::after {
    content: '';
    bottom: -20px;
    left: 50%;
    border: solid transparent;
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: #fb9678 transparent transparent;
    border-width: 10px;
    margin-left: -10px
}
@media (max-width: 1350px) {
    .carousel .item h3 {
        font-size: 17px;
        height: 90px
    }
    .inbox-center a {
        width: 400px
    }
}
.search-listing {
    padding: 0;
    margin: 0
}
.search-listing li {
    list-style: none;
    padding: 15px 0;
    border-bottom: 1px solid rgba(120, 130, 140, .13)
}
.search-listing li h3 {
    margin: 0;
    font-size: 18px
}
.search-listing li h3 a {
    color: #41b3f9
}
.search-listing li h3 a:hover {
    text-decoration: underline
}
.search-listing li a {
    color: #7ace4c
}
a.dt-button,
button.dt-button,
div.dt-button {
    background: #41b3f9;
    color: #fff;
    border-color: #41b3f9
}
a.dt-button:hover,
button.dt-button:hover,
div.dt-button:hover {
    background: #41b3f9
}
a.dt-button:hover:not(.disabled),
button.dt-button:hover:not(.disabled),
div.dt-button:hover:not(.disabled) {
    background: #f7fafc;
    color: #263238;
    border-color: rgba(120, 130, 140, .13)
}
.dataTables_filter input {
    border: 1px solid rgba(120, 130, 140, .13)
}
table.dataTable.display tbody tr.even>.sorting_1,
table.dataTable.display tbody tr.odd>.sorting_1,
table.dataTable.display tbody tr:hover>.sorting_1,
table.dataTable.order-column.hover tbody tr:hover>.sorting_1,
table.dataTable.order-column.stripe tbody tr.even>.sorting_1,
table.dataTable.order-column.stripe tbody tr.odd>.sorting_1 {
    background: 0 0
}
.note-editor {
    border: 1px solid rgba(120, 130, 140, .13)
}
.note-editor .panel-heading {
    padding: 6px 10px 10px
}
.page-aside {
    position: relative
}
.left-aside {
    position: absolute;
    background: #fff;
    border-right: 1px solid rgba(120, 130, 140, .13);
    padding: 20px;
    width: 250px;
    height: 100%
}
.right-aside {
    padding: 20px;
    margin-left: 250px
}
.right-aside .contact-list th {
    white-space: nowrap
}
.right-aside .contact-list td {
    vertical-align: middle;
    white-space: nowrap;
    padding: 25px 10px
}
.right-aside .contact-list td img {
    width: 30px
}
.contact-list th {
    white-space: nowrap
}
.contact-list td {
    vertical-align: middle;
    padding: 25px 10px;
    white-space: nowrap
}
.contact-list td img {
    width: 30px
}
.list-style-none {
    margin: 0;
    padding: 0
}
.list-style-none li {
    list-style: none;
    margin: 0
}
.list-style-none li.box-label a {
    font-weight: 500
}
.list-style-none li.divider {
    margin: 10px 0;
    height: 1px;
    background: rgba(120, 130, 140, .13)
}
.list-style-none li a {
    padding: 15px 10px;
    display: block;
    color: #313131
}
.list-style-none li a:hover {
    color: #2cabe3
}
.list-style-none li a span {
    float: right
}
.chat-main-box {
    position: relative;
    background: #fff;
    overflow: hidden
}
.chat-main-box .chat-left-aside {
    position: absolute;
    width: 250px;
    z-index: 9;
    top: 0;
    border-right: 1px solid rgba(120, 130, 140, .13)
}
.chat-main-box .chat-left-aside .open-panel {
    display: none;
    cursor: pointer;
    position: absolute;
    left: -webkit-calc(99%);
    top: 50%;
    z-index: 100;
    background-color: #fff;
    -webkit-box-shadow: 1px 0 3px rgba(0, 0, 0, .2);
    box-shadow: 1px 0 3px rgba(0, 0, 0, .2);
    border-radius: 0 100px 100px 0;
    line-height: 1;
    padding: 15px 8px 15px 4px
}
.chat-main-box .chat-left-aside .chat-left-inner .form-control {
    height: 60px
}
.chat-main-box .chat-left-aside .chat-left-inner .style-none {
    padding: 0
}
.chat-main-box .chat-left-aside .chat-left-inner .style-none li {
    list-style: none;
    overflow: hidden
}
.chat-main-box .chat-left-aside .chat-left-inner .style-none li a {
    padding: 20px
}
.chat-main-box .chat-left-aside .chat-left-inner .style-none li a.active,
.chat-main-box .chat-left-aside .chat-left-inner .style-none li a:hover {
    background: #f7fafc
}
.chat-main-box .chat-right-aside {
    margin-left: 250px
}
.chat-main-box .chat-right-aside .chat-list {
    max-height: none;
    height: 100%;
    padding-top: 40px
}
.chat-main-box .chat-right-aside .chat-list .chat-text {
    border-radius: 6px
}
.chat-main-box .chat-right-aside .send-chat-box {
    position: relative
}
.chat-main-box .chat-right-aside .send-chat-box .form-control {
    border: none;
    border-top: 1px solid rgba(120, 130, 140, .13);
    resize: none;
    height: 80px;
    padding-right: 180px
}
.chat-main-box .chat-right-aside .send-chat-box .form-control:focus {
    border-color: rgba(120, 130, 140, .13)
}
.chat-main-box .chat-right-aside .send-chat-box .custom-send {
    position: absolute;
    right: 20px;
    bottom: 10px
}
.chat-main-box .chat-right-aside .send-chat-box .custom-send .cst-icon {
    color: #313131;
    margin-right: 10px
}
.el-element-overlay .white-box {
    padding: 0
}
.el-element-overlay .el-card-item {
    position: relative;
    padding-bottom: 25px
}
.el-element-overlay .el-card-item .el-card-avatar {
    margin-bottom: 15px
}
.el-element-overlay .el-card-item .el-card-content {
    text-align: center
}
.el-element-overlay .el-card-item .el-card-content h3 {
    margin: 0
}
.el-element-overlay .el-card-item .el-card-content a {
    color: #313131
}
.el-element-overlay .el-card-item .el-card-content a:hover {
    color: #2cabe3
}
.el-element-overlay .el-card-item .el-overlay-1 {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    text-align: center;
    cursor: default
}
.el-element-overlay .el-card-item .el-overlay-1 img {
    display: block;
    position: relative;
    -webkit-transition: all .4s linear;
    transition: all .4s linear;
    width: 100%;
    height: auto
}
.el-element-overlay .el-card-item .el-overlay-1:hover img {
    -ms-transform: scale(1.2) translateZ(0);
    -webkit-transform: scale(1.2) translateZ(0)
}
.el-element-overlay .el-card-item .el-overlay-1 .el-info {
    text-decoration: none;
    display: inline-block;
    text-transform: uppercase;
    color: #fff;
    background-color: transparent;
    filter: alpha(opacity=0);
    -webkit-transition: all .2s ease-in-out;
    transition: all .2s ease-in-out;
    padding: 0;
    margin: auto;
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%) translateZ(0);
    -webkit-transform: translateY(-50%) translateZ(0);
    -ms-transform: translateY(-50%) translateZ(0)
}
.el-element-overlay .el-card-item .el-overlay-1 .el-info>li {
    list-style: none;
    display: inline-block;
    margin: 0 3px
}
.el-element-overlay .el-card-item .el-overlay-1 .el-info>li a {
    border-color: #fff;
    color: #fff;
    padding: 12px 15px 10px
}
.el-element-overlay .el-card-item .el-overlay-1 .el-info>li a:hover {
    background: #f33155;
    border-color: #f33155
}
.el-element-overlay .el-card-item .el-overlay {
    width: 100%;
    height: 100%;
    position: absolute;
    overflow: hidden;
    top: 0;
    left: 0;
    opacity: 0;
    background-color: rgba(0, 0, 0, .7);
    -webkit-transition: all .4s ease-in-out;
    transition: all .4s ease-in-out
}
.el-element-overlay .el-card-item .el-overlay-1:hover .el-overlay {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0)
}
.el-element-overlay .el-card-item .el-overlay-1 .scrl-dwn {
    top: -100%
}
.el-element-overlay .el-card-item .el-overlay-1 .scrl-up {
    top: 100%;
    height: 0
}
.el-element-overlay .el-card-item .el-overlay-1:hover .scrl-dwn {
    top: 0
}
.el-element-overlay .el-card-item .el-overlay-1:hover .scrl-up {
    top: 0;
    height: 100%
}
.login-sidebar {
    position: absolute;
    right: 0;
    margin-top: 0;
    height: 100%
}
.color-table.primary-table thead th {
    background-color: #7460ee;
    color: #fff
}
.color-table.success-table thead th {
    background-color: #7ace4c;
    color: #fff
}
.color-table.info-table thead th {
    background-color: #41b3f9;
    color: #fff
}
.color-table.warning-table thead th {
    background-color: #fb4;
    color: #fff
}
.color-table.danger-table thead th {
    background-color: #f33155;
    color: #fff
}
.color-table.inverse-table thead th {
    background-color: #4c5667;
    color: #fff
}
.color-table.dark-table thead th {
    background-color: #263238;
    color: #fff
}
.color-table.red-table thead th {
    background-color: #f33155;
    color: #fff
}
.color-table.purple-table thead th {
    background-color: #707cd2;
    color: #fff
}
.color-table.muted-table thead th {
    background-color: #98a6ad;
    color: #fff
}
.color-bordered-table.primary-bordered-table {
    border: 2px solid #7460ee
}
.color-bordered-table.primary-bordered-table thead th {
    background-color: #7460ee;
    color: #fff
}
.color-bordered-table.success-bordered-table {
    border: 2px solid #7ace4c
}
.color-bordered-table.success-bordered-table thead th {
    background-color: #7ace4c;
    color: #fff
}
.color-bordered-table.info-bordered-table {
    border: 2px solid #41b3f9
}
.color-bordered-table.info-bordered-table thead th {
    background-color: #41b3f9;
    color: #fff
}
.color-bordered-table.warning-bordered-table {
    border: 2px solid #fb4
}
.color-bordered-table.warning-bordered-table thead th {
    background-color: #fb4;
    color: #fff
}
.color-bordered-table.danger-bordered-table {
    border: 2px solid #f33155
}
.color-bordered-table.danger-bordered-table thead th {
    background-color: #f33155;
    color: #fff
}
.color-bordered-table.inverse-bordered-table {
    border: 2px solid #4c5667
}
.color-bordered-table.inverse-bordered-table thead th {
    background-color: #4c5667;
    color: #fff
}
.color-bordered-table.dark-bordered-table {
    border: 2px solid #263238
}
.color-bordered-table.dark-bordered-table thead th {
    background-color: #263238;
    color: #fff
}
.color-bordered-table.red-bordered-table {
    border: 2px solid #f33155
}
.color-bordered-table.red-bordered-table thead th {
    background-color: #f33155;
    color: #fff
}
.color-bordered-table.purple-bordered-table {
    border: 2px solid #707cd2
}
.color-bordered-table.purple-bordered-table thead th {
    background-color: #707cd2;
    color: #fff
}
.color-bordered-table.muted-bordered-table {
    border: 2px solid #98a6ad
}
.color-bordered-table.muted-bordered-table thead th {
    background-color: #98a6ad;
    color: #fff
}
.full-color-table.full-primary-table {
    background-color: rgba(171, 140, 228, .8)
}
.full-color-table.full-primary-table thead th {
    background-color: #7460ee;
    border: 0!important;
    color: #fff
}
.full-color-table.full-primary-table tbody td {
    border: 0!important;
    color: #fff
}
.full-color-table.full-primary-table tr:hover {
    background-color: #7460ee
}
.full-color-table.full-success-table {
    background-color: rgba(0, 194, 146, .8)
}
.full-color-table.full-success-table thead th {
    background-color: #7ace4c;
    border: 0!important;
    color: #fff
}
.full-color-table.full-success-table tbody td {
    border: 0!important;
    color: #fff
}
.full-color-table.full-success-table tr:hover {
    background-color: #7ace4c
}
.full-color-table.full-info-table {
    background-color: rgba(3, 169, 243, .8)
}
.full-color-table.full-info-table thead th {
    background-color: #41b3f9;
    border: 0!important;
    color: #fff
}
.full-color-table.full-info-table tbody td {
    border: 0!important;
    color: #fff
}
.full-color-table.full-info-table tr:hover {
    background-color: #41b3f9
}
.full-color-table.full-warning-table {
    background-color: rgba(254, 193, 7, .8)
}
.full-color-table.full-warning-table thead th {
    background-color: #fb4;
    border: 0!important;
    color: #fff
}
.full-color-table.full-warning-table tbody td {
    border: 0!important;
    color: #fff
}
.full-color-table.full-warning-table tr:hover {
    background-color: #fb4
}
.full-color-table.full-danger-table {
    background-color: rgba(251, 150, 120, .8)
}
.full-color-table.full-danger-table thead th {
    background-color: #f33155;
    border: 0!important;
    color: #fff
}
.full-color-table.full-danger-table tbody td {
    border: 0!important;
    color: #fff
}
.full-color-table.full-danger-table tr:hover {
    background-color: #f33155
}
.full-color-table.full-inverse-table {
    background-color: rgba(76, 86, 103, .8)
}
.full-color-table.full-inverse-table thead th {
    background-color: #4c5667;
    border: 0!important;
    color: #fff
}
.full-color-table.full-inverse-table tbody td {
    border: 0!important;
    color: #fff
}
.full-color-table.full-inverse-table tr:hover {
    background-color: #4c5667
}
.full-color-table.full-dark-table {
    background-color: rgba(43, 43, 43, .8)
}
.full-color-table.full-dark-table thead th {
    background-color: #263238;
    border: 0!important;
    color: #fff
}
.full-color-table.full-dark-table tbody td {
    border: 0!important;
    color: #fff
}
.full-color-table.full-dark-table tr:hover {
    background-color: #263238
}
.full-color-table.full-red-table {
    background-color: rgba(251, 58, 58, .8)
}
.full-color-table.full-red-table thead th {
    background-color: #f33155;
    border: 0!important;
    color: #fff
}
.full-color-table.full-red-table tbody td {
    border: 0!important;
    color: #fff
}
.full-color-table.full-red-table tr:hover {
    background-color: #f33155
}
.full-color-table.full-purple-table {
    background-color: rgba(150, 117, 206, .8)
}
.full-color-table.full-purple-table thead th {
    background-color: #707cd2;
    border: 0!important;
    color: #fff
}
.full-color-table.full-purple-table tbody td {
    border: 0!important;
    color: #fff
}
.full-color-table.full-purple-table tr:hover {
    background-color: #707cd2
}
.full-color-table.full-muted-table {
    background-color: rgba(152, 166, 173, .8)
}
.full-color-table.full-muted-table thead th {
    background-color: #98a6ad;
    border: 0!important;
    color: #fff
}
.full-color-table.full-muted-table tbody td {
    border: 0!important;
    color: #fff
}
.full-color-table.full-muted-table tr:hover {
    background-color: #98a6ad
}
.floating-labels .form-group {
    position: relative
}
.floating-labels .form-control {
    font-size: 20px;
    padding: 10px 10px 10px 0;
    display: block;
    border: none;
    border-bottom: 1px solid #e4e7ea
}
.floating-labels select.form-control>option {
    font-size: 14px
}
.has-error .form-control {
    border-bottom: 1px solid #f33155
}
.has-warning .form-control {
    border-bottom: 1px solid #fb4
}
.has-success .form-control {
    border-bottom: 1px solid #7ace4c
}
.floating-labels .form-control:focus {
    outline: 0;
    border: none
}
.floating-labels label {
    color: #313131;
    font-size: 16px;
    position: absolute;
    cursor: auto;
    font-weight: 400;
    top: 10px;
    transition: .2s ease all;
    -moz-transition: .2s ease all;
    -webkit-transition: .2s ease all
}
.floating-labels .form-control:focus~label,
.floating-labels .form-control:valid~label {
    top: -20px;
    font-size: 12px;
    color: #7460ee
}
.floating-labels .bar {
    position: relative;
    display: block
}
.floating-labels .bar:after,
.floating-labels .bar:before {
    content: '';
    height: 2px;
    width: 0;
    bottom: 1px;
    position: absolute;
    background: #7460ee;
    transition: .2s ease all;
    -moz-transition: .2s ease all;
    -webkit-transition: .2s ease all
}
.floating-labels .bar:before {
    left: 50%
}
.floating-labels .bar:after {
    right: 50%
}
.floating-labels .form-control:focus~.bar:after,
.floating-labels .form-control:focus~.bar:before {
    width: 50%
}
.floating-labels .highlight {
    position: absolute;
    height: 60%;
    width: 100px;
    top: 25%;
    left: 0;
    pointer-events: none;
    opacity: .5
}
.floating-labels .input-lg,
.floating-labels .input-lg~label {
    font-size: 24px
}
.floating-labels .input-sm,
.floating-labels .input-sm~label {
    font-size: 16px
}
.has-warning .bar:after,
.has-warning .bar:before {
    background: #fb4
}
.has-success .bar:after,
.has-success .bar:before {
    background: #7ace4c
}
.has-error .bar:after,
.has-error .bar:before {
    background: #f33155
}
.has-warning .form-control:focus~label,
.has-warning .form-control:valid~label {
    color: #fb4
}
.has-success .form-control:focus~label,
.has-success .form-control:valid~label {
    color: #7ace4c
}
.has-error .form-control:focus~label,
.has-error .form-control:valid~label {
    color: #f33155
}
.has-feedback label~.t-0 {
    top: 0
}
.table.dataTable,
table.dataTable {
    width: 99.8%!important
}
table.dataTable thead .sorting::after,
table.dataTable thead .sorting_asc::after,
table.dataTable thead .sorting_desc::after {
    float: none;
    padding-left: 10px
}
.re ul.two-part li i,
.re ul.two-part li span {
    font-size: 36px
}
.bg-light h4 {
    font-weight: 700
}
.agent-contact,
.pro-desc {
    font-size: 12px
}
.form-agent-inq .form-group {
    margin-bottom: 10px
}
.agent-info {
    max-height: 358px;
    height: 358px;
    background: #f7fafc
}
.pro-list {
    margin-top: 15px
}
.pro-detail,
.pro-img {
    display: table-cell;
    vertical-align: top
}
.pro-detail h5 a {
    color: #313131;
    line-height: 20px;
    font-weight: 500
}
.pro-box .pro-list-img {
    display: block;
    height: 210px;
    position: relative;
    overflow: hidden
}
.pro-box .pro-label {
    position: absolute;
    text-transform: uppercase;
    top: 0;
    right: 0;
    border-radius: 2px;
    padding: 5px;
    font-size: 80%
}
.pro-col-label {
    padding: 7px;
    width: 26%;
    display: block;
    margin-top: -15px;
    margin-left: 37%;
    border: 1px solid rgba(120, 130, 140, .13);
    text-transform: uppercase
}
.pro-box .pro-label-img {
    position: absolute;
    top: 30px;
    right: 30px
}
.pro-box.pro-horizontal pro-content {
    width: 100%;
    height: 210px
}
.pro-content .pro-list-details {
    height: 138px;
    max-height: 142px;
    border-bottom: 1px solid rgba(120, 130, 140, .13);
    border-right: 1px solid rgba(120, 130, 140, .13)
}
.pro-content .pro-list-info {
    border-bottom: 1px solid rgba(120, 130, 140, .13)
}
.pro-agent .agent-name h5,
.pro-agent .agent-name small,
.pro-agent-col-3 .agent-name h5,
.pro-agent-col-3 .agent-name small,
.pro-content .pro-list-details h3,
.pro-content .pro-list-details h4,
.pro-content-3-col .pro-list-details h3,
.pro-content-3-col .pro-list-details h4,
.pro-content-3-col .pro-list-details h4 small,
.pro-list-info ul.pro-info li,
.pro-list-info-3-col ul.pro-info li,
.pro-location span,
ul.pro-info li span.label {
    font-weight: 500
}
.pro-list-info ul.pro-info,
.pro-list-info-3-col ul.pro-info {
    padding: 16px 10px 10px;
    list-style: none
}
.pro-list-info ul.pro-info li {
    padding: 10px 0 10px 20px;
    font-size: 12px
}
ul.pro-info li span.label {
    width: 25px;
    height: 25px;
    padding: 8px;
    border-radius: 50%;
    margin-top: -4px;
    margin-right: 15px;
    font-size: 12px
}
ul.pro-amenities li span img,
ul.pro-info li span img {
    margin-top: -8px;
    padding-right: 12px
}
.pro-agent .agent-img a img,
.pro-agent-col-3 .agent-img a img {
    border: 3px solid #fff;
    box-shadow: 1px 1px 1px rgba(120, 130, 140, .13)
}
.pro-agent .agent-img,
.pro-agent .agent-name,
.pro-agent-col-3 .agent-img,
.pro-agent-col-3 .agent-name {
    float: left
}
.pro-agent .agent-img {
    padding-top: 12px
}
.pro-agent .agent-name {
    padding: 10px 0 0 15px
}
.pro-location span {
    padding-top: 27px
}
.pro-content-3-col {
    padding: 15px;
    background: #f7fafc
}
.pro-content-3-col .pro-list-details h4 small {
    color: #f33155
}
.pro-list-info-3-col ul.pro-info li {
    padding: 10px 5px
}
.pro-agent-col-3 .agent-img {
    padding: 15px
}
.pro-agent-col-3 .agent-name {
    padding: 15px 15px 15px 5px
}
ul.pro-amenities {
    list-style: none;
    padding: 8px 0
}
ul.pro-amenities li {
    padding: 10px 0;
    font-size: 12px
}
ul.pro-amenities li span i {
    padding-right: 12px
}
.pro-rd .table>tbody>tr>td:first-child {
    font-weight: 500
}
.pro-rd .table>tbody>tr>td,
.pro-rd .table>tbody>tr>th {
    border: none;
    padding: 8px 8px 8px 0;
    font-size: 12px
}
.pd-agent-info {
    max-height: 200px;
    height: 200px;
    background: #f7fafc;
    margin-top: 15px
}
.pd-agent-contact,
.pd-agent-inq {
    padding: 25px
}
.pro-add-form .checkbox label,
.pro-add-form .radio label {
    font-weight: 100
}
.plugin-details {
    display: none
}
.plugin-details-active {
    display: block
}
.register-box {
    max-width: 600px;
    margin: 0 auto;
    padding-top: 2%
}
.step-register {
    position: absolute;
    height: 100%
}
.material-icon-list-demo .icons div {
    width: 33%;
    padding: 15px;
    display: inline-block;
    line-height: 40px
}
.material-icon-list-demo .icons div i {
    font-size: 24px;
    vertical-align: middle;
    margin-right: 10px
}
.material-icon-list-demo .icons div:hover {
    background: #f7fafc
}
.full-width {
    display: inline-block;
    width: 100%;
    height: auto
}
@media (max-width: 1680px) {
    .weather-with-bg .wt-counter li {
        padding: 10px 1px
    }
}
@media (max-width: 1460px) {
    .weather-with-bg .wt-counter li {
        padding: 10px 0
    }
    .weather-with-bg .wt-counter li a {
        min-width: 38px;
        margin-bottom: 7px;
        height: 43px;
        padding: 10px
    }
}
@media (max-width: 1350px) {
    .carousel .item h3 {
        font-size: 17px;
        height: 90px
    }
    .inbox-center a {
        width: 400px
    }
    .new-login-register .lg-info-panel {
        width: 450px
    }
    .new-login-register .new-login-box {
        margin-left: 500px
    }
}
@media (min-width: 1170px) {
    .app-search .form-control:focus {
        width: 300px
    }
    .hide-sidebar .top-left-part {
        width: auto
    }
    .hide-sidebar .top-left-part .logo span {
        display: none
    }
    .hide-sidebar .sidebar {
        left: -240px;
        transition: .5s ease-out
    }
    .hide-sidebar #page-wrapper {
        margin-left: 0
    }
    .hide-sidebar .footer {
        left: 0
    }
}
@media (min-width: 768px) {
    #page-wrapper {
        position: inherit;
        margin: 0 0 0 240px
    }
    .navbar-default {
        position: relative;
        width: 100%;
        top: 0
    }
    .sidebar {
        z-index: 1001;
        position: fixed;
        width: 240px;
        padding-top: 0;
        height: 100%;
        transition: .05s ease-in
    }
    .sidebar:hover {
        width: 240px
    }
    .fix-header .navbar-static-top {
        position: fixed;
        z-index: 1010
    }
    .fix-header #page-wrapper {
        margin-top: 60px
    }
}
.navbar-top-links .dropdown-alerts,
.navbar-top-links .dropdown-messages,
.navbar-top-links .dropdown-tasks {
    margin-left: auto
}
.mail_listing {
    border-left: 1px solid rgba(120, 130, 140, .13);
    padding-left: 20px
}
.inbox-panel {
    padding-right: 20px
}
.top-minus {
    margin-top: -62px;
    float: right
}
@media (max-width: 1170px) {
    .content-wrapper .sidebar {
        left: -240px
    }
    .content-wrapper #page-wrapper {
        margin-left: 0
    }
    .content-wrapper .footer,
    .content-wrapper.show-sidebar .sidebar {
        left: 0
    }
    .col-in {
        padding: 15px 0
    }
    .col-in li.col-middle {
        width: 100%
    }
}
@media (max-width: 1023px) {
    .b-r-none {
        border-right: 0
    }
    .carousel-inner h3 {
        height: 90px;
        overflow: hidden
    }
    .inbox-center a {
        width: 300px
    }
    .new-login-register .lg-info-panel {
        display: none
    }
    .new-login-register .new-login-box {
        margin: 10% auto 0
    }
}
@media (max-width: 767px) {
    .navbar-top-links {
        float: left
    }
    .navbar-top-links .profile-pic img {
        margin-right: 0
    }
    .top-left-part {
        width: 60px
    }
    .navbar-top-links>li:last-child {
        margin-right: 0
    }
    .navbar-top-links>li>a {
        padding: 0 12px
    }
    .navbar-top-links .dropdown-alerts,
    .navbar-top-links .dropdown-messages,
    .navbar-top-links .dropdown-tasks {
        width: 260px
    }
    .show-sidebar .sidebar {
        width: 240px;
        top: 0
    }
    .show-sidebar .sidebar .hide-menu {
        display: inline
    }
    .show-sidebar .sidebar .nav-small-cap {
        display: block
    }
    .show-sidebar .sidebar .sidebar-head {
        width: 240px;
        display: block
    }
    .sidebar {
        z-index: 1001;
        position: fixed;
        width: 0;
        padding-top: 0;
        height: 100%
    }
    .sidebar-head {
        width: 0;
        display: none
    }
    #page-wrapper {
        margin: 0
    }
    .row-in-br {
        border-right: 0;
        border-bottom: 1px solid rgba(120, 130, 140, .13)
    }
    .bg-title .breadcrumb {
        float: left;
        margin-top: 0;
        margin-bottom: 10px
    }
    ul.timeline:before {
        left: 40px
    }
    ul.timeline>li>.timeline-panel {
        width: calc(100% - 90px)
    }
    ul.timeline>li>.timeline-badge {
        top: 16px;
        left: 15px;
        margin-left: 0
    }
    ul.timeline>li>.timeline-panel {
        float: right
    }
    ul.timeline>li>.timeline-panel:before {
        right: auto;
        left: -15px;
        border-right-width: 15px;
        border-left-width: 0
    }
    ul.timeline>li>.timeline-panel:after {
        right: auto;
        left: -14px;
        border-right-width: 14px;
        border-left-width: 0
    }
    .wizard-steps>li {
        display: block
    }
    .dropdown .dropdown-tasks,
    .dropdown .mailbox {
        left: -94px
    }
    .fix-header .navbar-static-top {
        position: fixed;
        top: 0;
        width: 100%
    }
    .fix-header #page-wrapper {
        margin-top: 60px
    }
    .mega-dropdown-menu {
        height: 340px;
        overflow: auto
    }
    .left-aside {
        position: relative;
        width: 100%;
        border: 0
    }
    .right-aside {
        margin-left: 0
    }
    .chat-main-box .chat-left-aside {
        left: -250px;
        transition: .5s ease-in;
        background: #fff
    }
    .chat-main-box .chat-left-aside.open-pnl {
        left: 0
    }
    .chat-main-box .chat-left-aside .open-panel {
        display: block
    }
    .chat-main-box .chat-right-aside {
        margin: 0
    }
    .table-responsive.pro-rd {
        border: none
    }
    #msform fieldset,
    .login-register,
    .step-register {
        position: relative
    }
    .mega-dropdown-menu {
        padding-left: 20px
    }
    .calendar-widget .cal-left {
        position: relative;
        width: 100%
    }
    .calendar-widget .cal-left .cal-btm-text {
        position: relative;
        bottom: 0;
        padding-top: 30px
    }
    .calendar-widget .cal-right {
        width: 100%
    }
    .calendar-widget .cal-right .cal-table td {
        padding: 15px 0
    }
    .calendar-widget .cal-right .cal-table td h1 {
        padding-left: 20px
    }
    .error-body h1 {
        font-size: 80px;
        line-height: 100px
    }
    .weather-with-bg .wt-top .wt-img h1 {
        font-size: 24px;
        line-height: 24px
    }
    .manage-table {
        margin: 0
    }
    .dp-table img {
        width: 50px
    }
    .earning-box li .er-row .er-text {
        width: 37%
    }
    .earning-box li .er-row .er-count {
        font-size: 24px
    }
    .sidebar .nav-second-level li a,
    .sidebar:hover .nav-second-level li a {
        padding-left: 40px
    }
    .sidebar .nav-third-level li a,
    .sidebar:hover .nav-third-level li a {
        padding-left: 60px
    }
}
@media (max-width: 480px) {
    .vtabs .tabs-vertical {
        width: auto
    }
    .stat-item {
        padding-right: 0
    }
    .login-box {
        width: 100%
    }
    .pro-content .pro-list-details {
        height: 100px;
        border-right: none
    }
    .pro-list-info ul.pro-info li {
        padding: 10px 0
    }
    .pro-list-info ul.pro-info {
        padding-left: 0
    }
    .pro-agent .agent-img {
        padding-top: 3px
    }
    .pro-agent .agent-name {
        padding: 2px 0 10px 15px
    }
    .new-login-register .lg-info-panel {
        display: none
    }
    .new-login-register .new-login-box {
        margin: 10% auto 0;
        width: 300px
    }
}

.currency-logo-sprite {
    margin-right: 8px;
    margin-top: 1px;
    float: left;
}
