.DS_Store
.env
*.tar.gz
*.log.*
t.js
node_modules
npm-debug.log
db.json
dump
conf/secret.json
github.pub
github
config.js
config.patch
data/*
conf.js
conf-*
conf_*
!conf-sample.js
sim_result*
trade_result*
paper_result*
*_test
backtesting_*
generation_data_*
simulations/*
models/**/*.json
models/**/*.html
*.pyc
*.swp
temp.html
logs
.sync
dist/*
.idea
*.iml
simulations/sim_*.json
gen.*.bat
gen.*.sh
.env
scripts/auto_backtester/backtesting_*.csv
database/*
.vscode/
