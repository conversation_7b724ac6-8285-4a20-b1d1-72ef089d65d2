KINDLY READ AND ACT ON THE BELOW INFORMATION BEFORE FILING YOUR ISSUE!

Please go to our Zenbot subreddit for questions, help and support:

[https://www.reddit.com/r/zenbot/](https://www.reddit.com/r/zenbot/)

If you open a GitHub issue, here is our policy:

1. It must be a bug or a feature request.
2. The form below must be filled out.

**Here's why we have that policy**: Zenbot developers respond to issues. We want to focus on work that benefits the whole community, e.g., fixing bugs and adding features. Support only helps individuals and rarely leads to bugfixes or useful enhancements. GitHub also notifies hundreds of people when issues are filed. We want them to see you communicating an interesting problem, rather than being redirected to Reddit.

------------------------
Remove everything above before creating your issue.
------------------------

### System information
- **Have I written custom code (as opposed to using zenbot vanilla)**:
- **OS Platform and Distribution (e.g., Linux Ubuntu 16.04)**:
- **Zenbot version** (commit ref, or version):
- **Zenbot branch**:
- **NodeJS version**:
- **Python version (when using a python script)**:
- **Exact command to reproduce (include everything)**:
- **Did I make any changes to conf-sample.js?**:

### Describe the problem
Describe the problem clearly here. Be sure to convey here why it's a bug in Zenbot or a feature request.

### Source code / Error logs
Include any logs or source code that would be helpful to diagnose the problem. If including tracebacks, please include the full traceback. Large logs and files should be attached. Try to provide a reproducible test case that is the bare minimum necessary to generate the problem.
