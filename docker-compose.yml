version: "3.1"
services:
  server:
    build:
      context: .
      dockerfile: Dockerfile
    image: deviavir/zenbot:unstable
    volumes:
      - ./simulations/:/app/simulations/
      - ./conf.js:/app/conf.js:ro
    restart: always
    tty: true
    ports:
      - "17365:17365"
    depends_on:
      - mongodb
    environment:
      - ZENBOT_MONGODB_HOST=mongodb
    stdin_open: true
  mongodb:
    image: mongo:latest
    volumes:
      - ./database:/data/db
    command: mongod --bind_ip=0.0.0.0 --logpath=/dev/null
  # Remove below comments to use this container. "adminMongo is a Web based user interface (GUI) to handle all your MongoDB connections/databases needs."
  #
  #adminmongo:
  #  image: mrvautin/adminmongo
  #  links:
  #    - mongodb
  #  tty: true
  #  ports:
  #    - "127.0.0.1:1234:1234"
  #  environment:
  #    - CONN_NAME=zenbot_mongodb
  #    - DB_HOST=mongodb
  #    - DB_PORT=27017
  #    - HOST=0.0.0.0
  #  command: "npm start"
