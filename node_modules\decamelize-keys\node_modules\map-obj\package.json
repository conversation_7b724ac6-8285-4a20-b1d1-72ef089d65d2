{"name": "map-obj", "version": "1.0.1", "description": "Map object keys and values into a new object", "license": "MIT", "repository": "sindresorhus/map-obj", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["map", "obj", "object", "key", "keys", "value", "values", "val", "iterate", "iterator"], "devDependencies": {"ava": "0.0.4"}}