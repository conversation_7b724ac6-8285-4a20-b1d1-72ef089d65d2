[{"asset": "AAVE", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Aave/Bitcoin"}, {"asset": "AAVE", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Aave/Tether USD"}, {"asset": "ADABEAR", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Short Cardano Token/Tether USD"}, {"asset": "ADABULL", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Long Cardano Token/Tether USD"}, {"asset": "ADD", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "ADD/Tether USD"}, {"asset": "ADEL", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Akropolis Delphi/Tether USD"}, {"asset": "AKITA", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Akita <PERSON>/Tether USD"}, {"asset": "AKRO", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Akropolis/Bitcoin"}, {"asset": "AKRO", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Akropolis/Tether USD"}, {"asset": "ALPHA", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Alpha Finance/Tether USD"}, {"asset": "AMP", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Amp/Bitcoin"}, {"asset": "AMP", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Amp/Tron"}, {"asset": "AMP", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Amp/Tether USD"}, {"asset": "API3", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "API3/Tether USD"}, {"asset": "ARDR", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Ardor/Bitcoin"}, {"asset": "ATOM", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Cosmos/Bitcoin"}, {"asset": "ATOM", "currency": "USDC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Cosmos/USD Coin"}, {"asset": "ATOM", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Cosmos/Tether USD"}, {"asset": "AVA", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Travala.com Token/Bitcoin"}, {"asset": "AVA", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Travala.com Token/Tron"}, {"asset": "AVA", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Travala.com Token/Tether USD"}, {"asset": "B20", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "B20/Tether USD"}, {"asset": "BAC", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Basis Cash/Tether USD"}, {"asset": "BADGER", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Badger DAO/Tether USD"}, {"asset": "BAL", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Balancer/Tether USD"}, {"asset": "BAND", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Band Protocol Token/Tether USD"}, {"asset": "BAT", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Basic Attention Token/Bitcoin"}, {"asset": "BAT", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Basic Attention Token/Tether USD"}, {"asset": "BCH", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Bitcoin Cash/Bitcoin"}, {"asset": "BCH", "currency": "USDC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Bitcoin Cash/USD Coin"}, {"asset": "BCH", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Bitcoin Cash/Tether USD"}, {"asset": "BCHA", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Bitcoin Cash ABC/Bitcoin"}, {"asset": "BCHA", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Bitcoin Cash ABC/Tether USD"}, {"asset": "BCHBEAR", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Short Bitcoin Cash Token/Tether USD"}, {"asset": "BCHBULL", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Long Bitcoin Cash Token/Tether USD"}, {"asset": "BCHC", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "BitCherry/Tether USD"}, {"asset": "BCHSV", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Bitcoin SV/Bitcoin"}, {"asset": "BCHSV", "currency": "USDC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Bitcoin SV/USD Coin"}, {"asset": "BCHSV", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Bitcoin SV/Tether USD"}, {"asset": "BCN", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Bytecoin/Tether USD"}, {"asset": "BDP", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Big Data Protocol/Tether USD"}, {"asset": "BEAR", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Short Bitcoin Token/Tether USD"}, {"asset": "BID", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Bidao/Bitcoin"}, {"asset": "BID", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Bidao/Tether USD"}, {"asset": "BLY", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Blocery/Tether USD"}, {"asset": "BNB", "currency": "BUSD", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Binance Coin/Binance USD"}, {"asset": "BNB", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Binance Coin/Tron"}, {"asset": "BNB", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Binance Coin/Tether USD"}, {"asset": "BNT", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Bancor/Bitcoin"}, {"asset": "BOND", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "BarnBridge/Tether USD"}, {"asset": "BRG", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Bridge Oracle/Tron"}, {"asset": "BRG", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Bridge Oracle/Tether USD"}, {"asset": "BSVBEAR", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Short Bitcoin SV Token/Tether USD"}, {"asset": "BSVBULL", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Long Bitcoin SV Token/Tether USD"}, {"asset": "BTC", "currency": "BNB", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Bitcoin/Binance Coin"}, {"asset": "BTC", "currency": "BUSD", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Bitcoin/Binance USD"}, {"asset": "BTC", "currency": "DAI", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Bitcoin/Dai Stablecoin"}, {"asset": "BTC", "currency": "PAX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Bitcoin/Paxos Standard"}, {"asset": "BTC", "currency": "TUSD", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Bitcoin/TrueUSD"}, {"asset": "BTC", "currency": "USDC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Bitcoin/USD Coin"}, {"asset": "BTC", "currency": "USDJ", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Bitcoin/USDJ"}, {"asset": "BTC", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Bitcoin/Tether USD"}, {"asset": "BTCST", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Bitcoin Standard Hashrate Token/Tether USD"}, {"asset": "BTS", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "BitShares/Bitcoin"}, {"asset": "BTT", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "BitTorrent/Tron"}, {"asset": "BTT", "currency": "USDJ", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "BitTorrent/USDJ"}, {"asset": "BTT", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "BitTorrent/Tether USD"}, {"asset": "BULL", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Long Bitcoin Token/Tether USD"}, {"asset": "BUSD", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Binance USD/Tether USD"}, {"asset": "BVOL", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Bitcoin Volatility Token/Tether USD"}, {"asset": "BZRX", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "bZx Protocol Token/Tether USD"}, {"asset": "CHR", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Chromia/Bitcoin"}, {"asset": "CHR", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Chromia/Tron"}, {"asset": "CHR", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Chromia/Tether USD"}, {"asset": "COMBO", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Furucombo/Tether USD"}, {"asset": "COMP", "currency": "ETH", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Compound Governance Token/Ethereum"}, {"asset": "COMP", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Compound Governance Token/Tether USD"}, {"asset": "CORN", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Corn/Tether USD"}, {"asset": "CREAM", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Cream/Tether USD"}, {"asset": "CRV", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Curve Finance/Tether USD"}, {"asset": "CTSI", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Cartesi <PERSON> /Tether USD"}, {"asset": "CUDOS", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "CudosToken/Tether USD"}, {"asset": "CUSDT", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Compound USDT/Tether USD"}, {"asset": "CVC", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Civic/Bitcoin"}, {"asset": "CVP", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "PowerPool Concentrated Vot Power/Tether USD"}, {"asset": "CVT", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "CyberVeinToken/Bitcoin"}, {"asset": "CVT", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "CyberVeinToken/Tether USD"}, {"asset": "DAI", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Dai Stablecoin/Tether USD"}, {"asset": "DASH", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Dash/Bitcoin"}, {"asset": "DASH", "currency": "USDC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Dash/USD Coin"}, {"asset": "DASH", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Dash/Tether USD"}, {"asset": "DCR", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Decred/Bitcoin"}, {"asset": "DEC", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Decentr/Tether USD"}, {"asset": "DEGO", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Dego Finance/Tether USD"}, {"asset": "DEXT", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "DexTools/Tether USD"}, {"asset": "DHT", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "dHedge DAO/Tether USD"}, {"asset": "DIA", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "DIA/Tether USD"}, {"asset": "DICE", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "TRONbetDice/Tron"}, {"asset": "DICE", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "TRONbetDice/Tether USD"}, {"asset": "DOGE", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Dogecoin/Bitcoin"}, {"asset": "DOGE", "currency": "USDC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Dogecoin/USD Coin"}, {"asset": "DOGE", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Dogecoin/Tether USD"}, {"asset": "DOS", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "DOS Network/Tether USD"}, {"asset": "DOT", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Polkadot/Bitcoin"}, {"asset": "DOT", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Polkadot/Tether USD"}, {"asset": "ELON", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Dogelon/Tether USD"}, {"asset": "EOS", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "EOS/Bitcoin"}, {"asset": "EOS", "currency": "ETH", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "EOS/Ethereum"}, {"asset": "EOS", "currency": "USDC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "EOS/USD Coin"}, {"asset": "EOS", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "EOS/Tether USD"}, {"asset": "EOSBEAR", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Short EOS Token/Tether USD"}, {"asset": "EOSBULL", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Long EOS Token/Tether USD"}, {"asset": "ESD", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Empty Set Dollar/Tether USD"}, {"asset": "ETC", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Ethereum Classic/Bitcoin"}, {"asset": "ETC", "currency": "ETH", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Ethereum Classic/Ethereum"}, {"asset": "ETC", "currency": "USDC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Ethereum Classic/USD Coin"}, {"asset": "ETC", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Ethereum Classic/Tether USD"}, {"asset": "ETH", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Ethereum/Bitcoin"}, {"asset": "ETH", "currency": "DAI", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Ethereum/Dai Stablecoin"}, {"asset": "ETH", "currency": "PAX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Ethereum/Paxos Standard"}, {"asset": "ETH", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Ethereum/Tron"}, {"asset": "ETH", "currency": "TUSD", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Ethereum/TrueUSD"}, {"asset": "ETH", "currency": "USDC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Ethereum/USD Coin"}, {"asset": "ETH", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Ethereum/Tether USD"}, {"asset": "ETHBEAR", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Short Ethereum Token/Tether USD"}, {"asset": "ETHBULL", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Long Ethereum Token/Tether USD"}, {"asset": "EXE", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "8X8 Protocol/Bitcoin"}, {"asset": "EXE", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "8X8 Protocol/Tether USD"}, {"asset": "FARM", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Harvest Finance/Bitcoin"}, {"asset": "FARM", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Harvest Finance/Tether USD"}, {"asset": "FCT2", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Firmachain/Bitcoin"}, {"asset": "FCT2", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Firmachain/Tether USD"}, {"asset": "FIL", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Filecoin/Bitcoin"}, {"asset": "FIL", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Filecoin/Tether USD"}, {"asset": "FOAM", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Foam/Bitcoin"}, {"asset": "FORTH", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Ampleforth Governance Token/Tether USD"}, {"asset": "FRONT", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Frontier/Bitcoin"}, {"asset": "FRONT", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Frontier/Tether USD"}, {"asset": "FSW", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "FalconSwap/Tether USD"}, {"asset": "FTT", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "FTX Token/Tether USD"}, {"asset": "FUND", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Unification/Bitcoin"}, {"asset": "FUND", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Unification/Tron"}, {"asset": "FUND", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Unification/Tether USD"}, {"asset": "GAS", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Gas/Bitcoin"}, {"asset": "GEEQ", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "GEEQ/Tether USD"}, {"asset": "GHST", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Aavegotchi/Tether USD"}, {"asset": "GLM", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Golem Network Token/Bitcoin"}, {"asset": "GLM", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Golem Network Token/Tether USD"}, {"asset": "GRT", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "The Graph/Tether USD"}, {"asset": "GTC", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Gitcoin/Tether USD"}, {"asset": "HEGIC", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Hegic/Tether USD"}, {"asset": "HGET", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Hedget/Bitcoin"}, {"asset": "HGET", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Hedget/Tether USD"}, {"asset": "HT", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "<PERSON><PERSON><PERSON>/Tether USD"}, {"asset": "IBVOL", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Inverse Bitcoin Volatility Token/Tether USD"}, {"asset": "INJ", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Injective Protocol/Bitcoin"}, {"asset": "INJ", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Injective Protocol/Tether USD"}, {"asset": "JFI", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "JackPool.finance/Tether USD"}, {"asset": "JST", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Just/Tron"}, {"asset": "JST", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Just/Tether USD"}, {"asset": "KCS", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "KuCoin <PERSON>/Tether USD"}, {"asset": "KISHU", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Kishu <PERSON> /<PERSON>"}, {"asset": "KLV", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Klever/Bitcoin"}, {"asset": "KLV", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Klever/Tron"}, {"asset": "KLV", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Klever/Tether USD"}, {"asset": "KNC", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Kyber/Bitcoin"}, {"asset": "KP3R", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Keep3rV1/Tether USD"}, {"asset": "KTON", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Darwinia Commitment Token/Tether USD"}, {"asset": "LINK", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Chainlink/Bitcoin"}, {"asset": "LINK", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Chainlink/Tron"}, {"asset": "LINK", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Chainlink/Tether USD"}, {"asset": "LINKBEAR", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Short Chainlink Token/Tether USD"}, {"asset": "LINKBULL", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Long Chainlink Token/Tether USD"}, {"asset": "LIVE", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "TRONbetLive/Tether USD"}, {"asset": "LON", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Tokenlon/Tether USD"}, {"asset": "LOOM", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "LOOM Network/Bitcoin"}, {"asset": "LPT", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Livepeer/Bitcoin"}, {"asset": "LPT", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Livepeer/Tether USD"}, {"asset": "LQTY", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "LQTY/Tether USD"}, {"asset": "LRC", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Loopring/Bitcoin"}, {"asset": "LRC", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Loopring/Tether USD"}, {"asset": "LSK", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Lisk/Bitcoin"}, {"asset": "LSK", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Lisk/Tether USD"}, {"asset": "LTC", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Litecoin/Bitcoin"}, {"asset": "LTC", "currency": "USDC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Litecoin/USD Coin"}, {"asset": "LTC", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Litecoin/Tether USD"}, {"asset": "LTCBEAR", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Short Litecoin Token/Tether USD"}, {"asset": "LTCBULL", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Long Litecoin Token/Tether USD"}, {"asset": "LUSD", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "LUSD Stablecoin/Tether USD"}, {"asset": "MANA", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Decentraland/Bitcoin"}, {"asset": "MANA", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Decentraland/Tether USD"}, {"asset": "MATIC", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Polygon/Bitcoin"}, {"asset": "MATIC", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Polygon/Tron"}, {"asset": "MATIC", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Polygon/Tether USD"}, {"asset": "MCB", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "MCDex/Tether USD"}, {"asset": "MDT", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Measurable Data Token/Bitcoin"}, {"asset": "MDT", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Measurable Data Token/Tron"}, {"asset": "MDT", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Measurable Data Token/Tether USD"}, {"asset": "MEME", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Meme/Tether USD"}, {"asset": "MIR", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Wrapped Mirror Token/Tether USD"}, {"asset": "MIST", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Alchemist/Tether <PERSON>"}, {"asset": "MKR", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Maker/Bitcoin"}, {"asset": "MKR", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Maker/Tether USD"}, {"asset": "MPH", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "88mph/Tether USD"}, {"asset": "MTA", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Meta/Tether USD"}, {"asset": "MVL", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Mass Vehicle Ledger Token/Tether USD"}, {"asset": "NEO", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Neo/Bitcoin"}, {"asset": "NEO", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Neo/Tron"}, {"asset": "NEO", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Neo/Tether USD"}, {"asset": "NFT", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "APENFT/Tron"}, {"asset": "NFT", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "APENFT/Tether USD"}, {"asset": "NFTX", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "NFTX/Tether USD"}, {"asset": "NMR", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Numeraire/Bitcoin"}, {"asset": "NU", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "NuCypher/Tether USD"}, {"asset": "NXT", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "NXT/Bitcoin"}, {"asset": "OCEAN", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Ocean Protocol/Tether USD"}, {"asset": "OM", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "MANTRA DAO/Tether USD"}, {"asset": "OMG", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "OmiseGO/Bitcoin"}, {"asset": "ONEINCH", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "1INCH Token/Tether USD"}, {"asset": "PAX", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Paxos Standard/Tether USD"}, {"asset": "PBTC35A", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Poolin pBTC35A/Tether USD"}, {"asset": "PEARL", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Pearl Finance/Tether USD"}, {"asset": "PERX", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "PeerEx Network/Tether USD"}, {"asset": "POLS", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Polkastarter/Tether USD"}, {"asset": "POLY", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Polymath/Bitcoin"}, {"asset": "PRQ", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "PARSIQ/Tether USD"}, {"asset": "QTUM", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Qtum/Bitcoin"}, {"asset": "QTUM", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Qtum/Tether USD"}, {"asset": "QUICK", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Quickswap/Tether USD"}, {"asset": "RARI", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Rarible/Tether USD"}, {"asset": "REEF", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Reef Finance/Tether USD"}, {"asset": "REN", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Ren/Bitcoin"}, {"asset": "REN", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Ren/Tether USD"}, {"asset": "REPV2", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Augur/Bitcoin"}, {"asset": "REPV2", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Augur/Tether USD"}, {"asset": "RFUEL", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Rio DeFi/Tether USD"}, {"asset": "RING", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Darwinia Network Native Token/Tether USD"}, {"asset": "RLC", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "RLC/Tether USD"}, {"asset": "ROOK", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "KeeperDAO/Tether USD"}, {"asset": "RSR", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Reserve Rights Token/Tether USD"}, {"asset": "SAND", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "The Sandbox/Bitcoin"}, {"asset": "SAND", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "The Sandbox/Tether USD"}, {"asset": "SC", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Siacoin/Bitcoin"}, {"asset": "SC", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Siacoin/Tether USD"}, {"asset": "SENSO", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "SENSO/Bitcoin"}, {"asset": "SENSO", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "SENSO/Tether USD"}, {"asset": "SFI", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "saffron.finance/Tether USD"}, {"asset": "SHIB", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Shiba Inu/Tether USD"}, {"asset": "SNT", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Status/Bitcoin"}, {"asset": "SNX", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Synthetix Network Token/Bitcoin"}, {"asset": "SNX", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Synthetix Network Token/Tron"}, {"asset": "SNX", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Synthetix Network Token/Tether USD"}, {"asset": "SRM", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Serum/Tether USD"}, {"asset": "STAKE", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "xDAI Stake/Tether USD"}, {"asset": "STEEM", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "STEEM/Bitcoin"}, {"asset": "STEEM", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "STEEM/Tron"}, {"asset": "STEEM", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "STEEM/Tether USD"}, {"asset": "STORJ", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Storj/Bitcoin"}, {"asset": "STPT", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Standard Tokenization Protocol/Bitcoin"}, {"asset": "STPT", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Standard Tokenization Protocol/Tether USD"}, {"asset": "STR", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Stellar/Bitcoin"}, {"asset": "STR", "currency": "USDC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Stellar/USD Coin"}, {"asset": "STR", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Stellar/Tether USD"}, {"asset": "SUN", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Sun/Tron"}, {"asset": "SUN", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Sun/Tether USD"}, {"asset": "SUSHI", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "SushiSwap/Tether USD"}, {"asset": "SWAP", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Trustswap/Bitcoin"}, {"asset": "SWAP", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Trustswap/Tether USD"}, {"asset": "SWFTC", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "SwftCoin/Bitcoin"}, {"asset": "SWFTC", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "SwftCoin/Tron"}, {"asset": "SWFTC", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "SwftCoin/Tether USD"}, {"asset": "SWINGBY", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Swingby/Bitcoin"}, {"asset": "SWINGBY", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Swingby/Tron"}, {"asset": "SWINGBY", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Swingby/Tether USD"}, {"asset": "SWRV", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Swerve/Tether USD"}, {"asset": "SXP", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Swipe/Bitcoin"}, {"asset": "SXP", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Swipe/Tron"}, {"asset": "SXP", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Swipe/Tether USD"}, {"asset": "TAI", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "tBridge Token/Tether USD"}, {"asset": "TEND", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Tendies/Tether USD"}, {"asset": "TORN", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Tornado Cash/Tether USD"}, {"asset": "TRADE", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "UniTrade/Tether USD"}, {"asset": "TRB", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Tellor/Tether USD"}, {"asset": "TRU", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "TrueFi/Bitcoin"}, {"asset": "TRU", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "TrueFi/Tether USD"}, {"asset": "TRX", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Tron/Bitcoin"}, {"asset": "TRX", "currency": "USDC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Tron/USD Coin"}, {"asset": "TRX", "currency": "USDJ", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Tron/USDJ"}, {"asset": "TRX", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Tron/Tether USD"}, {"asset": "TRXBEAR", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Short TRX Token/Tether USD"}, {"asset": "TRXBULL", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Long TRX Token/Tether USD"}, {"asset": "TUSD", "currency": "USDC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "TrueUSD/USD Coin"}, {"asset": "TUSD", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "TrueUSD/Tether USD"}, {"asset": "UMA", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "UMA/Tether USD"}, {"asset": "UNI", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Uniswap/Tether USD"}, {"asset": "USDJ", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "USDJ/Tether USD"}, {"asset": "USDT", "currency": "USDC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Tether USD/USD Coin"}, {"asset": "UST", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "TerraUSD/Tether USD"}, {"asset": "VALUE", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Value Liquidity/Tether USD"}, {"asset": "VSP", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Vesper Finance/Tron"}, {"asset": "VSP", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Vesper Finance/Tether USD"}, {"asset": "WBTC", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Wrapped Bitcoin/Bitcoin"}, {"asset": "WBTC", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Wrapped Bitcoin/Tether USD"}, {"asset": "WETH", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Wrapped Ethereum on Tron/Tether USD"}, {"asset": "WHALE", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "WHALE/Tether USD"}, {"asset": "WIN", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "WINK/Tron"}, {"asset": "WIN", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "WINK/Tether USD"}, {"asset": "WNXM", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Wrapped NXM/Tether USD"}, {"asset": "WRX", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "WazirX/Bitcoin"}, {"asset": "WRX", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "WazirX/Tron"}, {"asset": "WRX", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "WazirX/Tether USD"}, {"asset": "XEM", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "NEM/Bitcoin"}, {"asset": "XFLR", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Spark [IOU]/Tether USD"}, {"asset": "XLMBEAR", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Short Stellar Token/Tether USD"}, {"asset": "XLMBULL", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Long Stellar Token/Tether USD"}, {"asset": "XMR", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Monero/Bitcoin"}, {"asset": "XMR", "currency": "USDC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Monero/USD Coin"}, {"asset": "XMR", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Monero/Tether USD"}, {"asset": "XOR", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Sora Token/Tether USD"}, {"asset": "XRP", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "XRP/Bitcoin"}, {"asset": "XRP", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "XRP/Tron"}, {"asset": "XRP", "currency": "USDC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "XRP/USD Coin"}, {"asset": "XRP", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "XRP/Tether USD"}, {"asset": "XRPBEAR", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Short XRP Token/Tether USD"}, {"asset": "XRPBULL", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "3X Long XRP Token/Tether USD"}, {"asset": "XTZ", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Tezos/Bitcoin"}, {"asset": "XTZ", "currency": "TRX", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Tezos/Tron"}, {"asset": "XTZ", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Tezos/Tether USD"}, {"asset": "XYM", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Symbol/Bitcoin"}, {"asset": "XYM", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Symbol/Tether USD"}, {"asset": "YFI", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "yearn.finance/Tether USD"}, {"asset": "YFII", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "DFI.money/Tether USD"}, {"asset": "YFL", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "YFLink/Tether USD"}, {"asset": "ZAP", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Zap/Tether USD"}, {"asset": "ZEC", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Zcash/Bitcoin"}, {"asset": "ZEC", "currency": "ETH", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Zcash/Ethereum"}, {"asset": "ZEC", "currency": "USDC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "Zcash/USD Coin"}, {"asset": "ZEC", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "Zcash/Tether USD"}, {"asset": "ZKS", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "ZKSwap/Tether USD"}, {"asset": "ZLOT", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "zLOT/Tether USD"}, {"asset": "ZRX", "currency": "BTC", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "0x/Bitcoin"}, {"asset": "ZRX", "currency": "ETH", "min_total": "0.0001", "max_size": null, "increment": "0.00000001", "label": "0x/Ethereum"}, {"asset": "ZRX", "currency": "USDT", "min_total": "1", "max_size": null, "increment": "0.00000001", "label": "0x/Tether USD"}]