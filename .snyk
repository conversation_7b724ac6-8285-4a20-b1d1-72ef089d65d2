# Snyk (https://snyk.io) policy file, patches or ignores known vulnerabilities.
version: v1.16.0
ignore: {}
# patches apply the minimum changes required to fix a vulnerability
patch:
  SNYK-JS-LODASH-450202:
    - adamant-api > bitcore-mnemonic > bitcore-lib > lodash:
        patched: '2020-03-25T09:47:35.751Z'
  SNYK-JS-LODASH-567746:
    - adamant-api > bitcore-mnemonic > bitcore-lib > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - '@babel/core > lodash':
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - gemini-api > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - node-sass > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - snyk > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - '@babel/core > @babel/helper-module-transforms > lodash':
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-block-scoping > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - extract-text-webpack-plugin > async > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - node-sass > sass-graph > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - snyk > graphlib > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - snyk > @snyk/ruby-semver > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - snyk > inquirer > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - snyk > snyk-config > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - snyk > snyk-mvn-plugin > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - snyk > snyk-nodejs-lockfile-parser > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - snyk > snyk-nuget-plugin > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - '@babel/core > @babel/helpers > @babel/traverse > lodash':
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-duplicate-keys > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-parameters > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-shorthand-properties > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-sticky-regex > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-parameters > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-sticky-regex > babel-helper-regex > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-unicode-regex > babel-helper-regex > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > request-promise > request-promise-core > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - node-telegram-bot-api > request-promise > request-promise-core > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - pushbullet > request-promise-native > request-promise-core > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - node-sass > gaze > globule > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - snyk > @snyk/dep-graph > graphlib > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - snyk > snyk-go-plugin > graphlib > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - snyk > snyk-nodejs-lockfile-parser > graphlib > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - snyk > @snyk/snyk-cocoapods-plugin > @snyk/dep-graph > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - snyk > snyk-mvn-plugin > @snyk/java-call-graph-builder > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - snyk > snyk-nuget-plugin > dotnet-deps-parser > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - snyk > snyk-php-plugin > @snyk/composer-lockfile-parser > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - '@babel/core > @babel/helper-module-transforms > @babel/helper-replace-supers > @babel/traverse > lodash':
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-parameters > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-optimise-call-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-plugin-transform-strict-mode > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-helper-hoist-variables > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-sticky-regex > babel-helper-regex > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-unicode-regex > babel-helper-regex > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-regenerator > regenerator-transform > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > request-promise > request-promise-core > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - snyk > @snyk/snyk-cocoapods-plugin > @snyk/dep-graph > graphlib > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - snyk > @snyk/snyk-cocoapods-plugin > @snyk/cocoapods-lockfile-parser > @snyk/ruby-semver > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - '@babel/core > @babel/helper-module-transforms > @babel/helper-replace-supers > @babel/traverse > @babel/generator > lodash':
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-helper-optimise-call-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-helper-optimise-call-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-duplicate-keys > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-plugin-transform-strict-mode > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-helper-hoist-variables > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-shorthand-properties > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-sticky-regex > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-helper-explode-assignable-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-plugin-transform-regenerator > regenerator-transform > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-helper-explode-assignable-expression > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-sticky-regex > babel-helper-regex > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-unicode-regex > babel-helper-regex > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - snyk > @snyk/snyk-cocoapods-plugin > @snyk/cocoapods-lockfile-parser > @snyk/dep-graph > graphlib > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - '@babel/core > @babel/helper-module-transforms > @babel/helper-replace-supers > @babel/traverse > @babel/helper-split-export-declaration > @babel/types > lodash':
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-helper-explode-assignable-expression > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-optimise-call-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-duplicate-keys > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-duplicate-keys > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-duplicate-keys > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-plugin-transform-strict-mode > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-plugin-transform-strict-mode > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-helper-hoist-variables > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-shorthand-properties > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-shorthand-properties > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-shorthand-properties > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-sticky-regex > babel-helper-regex > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-unicode-regex > babel-helper-regex > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-sticky-regex > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-sticky-regex > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-sticky-regex > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-regenerator > regenerator-transform > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-plugin-transform-regenerator > regenerator-transform > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-plugin-transform-regenerator > regenerator-transform > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-plugin-transform-regenerator > regenerator-transform > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-sticky-regex > babel-helper-regex > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-sticky-regex > babel-helper-regex > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-sticky-regex > babel-helper-regex > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-unicode-regex > babel-helper-regex > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-unicode-regex > babel-helper-regex > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-unicode-regex > babel-helper-regex > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - '@babel/core > @babel/helper-module-transforms > @babel/helper-replace-supers > @babel/traverse > @babel/helper-function-name > @babel/template > @babel/types > lodash':
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-helper-optimise-call-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-helper-optimise-call-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-optimise-call-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-optimise-call-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-optimise-call-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-duplicate-keys > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-plugin-transform-strict-mode > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-plugin-transform-strict-mode > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-plugin-transform-strict-mode > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-plugin-transform-strict-mode > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-helper-hoist-variables > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-helper-hoist-variables > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-helper-hoist-variables > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-helper-hoist-variables > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-shorthand-properties > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-sticky-regex > babel-helper-regex > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-sticky-regex > babel-helper-regex > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-sticky-regex > babel-helper-regex > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-unicode-regex > babel-helper-regex > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-unicode-regex > babel-helper-regex > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-unicode-regex > babel-helper-regex > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-sticky-regex > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-helper-explode-assignable-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-regenerator > regenerator-transform > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-regenerator > regenerator-transform > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-regenerator > regenerator-transform > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-plugin-transform-regenerator > regenerator-transform > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-helper-explode-assignable-expression > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-sticky-regex > babel-helper-regex > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-unicode-regex > babel-helper-regex > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-helper-explode-assignable-expression > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-helper-optimise-call-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-helper-optimise-call-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-helper-optimise-call-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-helper-optimise-call-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-helper-optimise-call-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-helper-optimise-call-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-optimise-call-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-plugin-transform-strict-mode > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-plugin-transform-strict-mode > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-plugin-transform-strict-mode > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-plugin-transform-strict-mode > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-plugin-transform-strict-mode > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-helper-hoist-variables > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-helper-hoist-variables > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-helper-hoist-variables > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-helper-hoist-variables > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-sticky-regex > babel-helper-regex > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-unicode-regex > babel-helper-regex > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-helper-explode-assignable-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-helper-explode-assignable-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-helper-explode-assignable-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-regenerator > regenerator-transform > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-helper-explode-assignable-expression > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-helper-explode-assignable-expression > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-helper-explode-assignable-expression > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-block-scoping > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-computed-properties > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-systemjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-helper-explode-assignable-expression > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-helper-explode-assignable-expression > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-helper-explode-assignable-expression > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-helper-optimise-call-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-helper-optimise-call-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-plugin-transform-strict-mode > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-plugin-transform-strict-mode > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-plugin-transform-strict-mode > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-plugin-transform-strict-mode > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-parameters > babel-helper-call-delegate > babel-helper-hoist-variables > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-helper-explode-assignable-expression > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-helper-explode-assignable-expression > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-helper-get-function-arity > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-function-name > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-replace-supers > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-object-super > babel-helper-replace-supers > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-ws1 > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-exponentiation-operator > babel-helper-builder-binary-assignment-operator-visitor > babel-helper-explode-assignable-expression > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-plugin-transform-strict-mode > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-async-to-generator > babel-helper-remap-async-to-generator > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-classes > babel-helper-define-map > babel-helper-function-name > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-api-node-util > babel-preset-env > babel-plugin-transform-es2015-modules-umd > babel-plugin-transform-es2015-modules-amd > babel-plugin-transform-es2015-modules-commonjs > babel-template > babel-traverse > babel-types > lodash:
        patched: '2020-05-01T07:02:24.711Z'
    - bitfinex-api-node > bfx-api-node-models > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > blessed-contrib > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-hf-util > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-hf-util > @babel/cli > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-hf-util > create-index > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-hf-util > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-hf-util > @babel/preset-es2015 > @babel/plugin-transform-block-scoping > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-hf-util > @babel/cli > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-hf-util > babel-eslint > babel-types > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-hf-util > babel-eslint > babel-traverse > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-hf-util > create-index > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-hf-util > @babel/preset-es2015 > @babel/plugin-transform-unicode-regex > @babel/helper-regex > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-hf-util > @babel/preset-es2015 > @babel/plugin-transform-block-scoping > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-hf-util > babel-eslint > babel-traverse > babel-types > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-hf-util > babel-eslint > babel-types > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-hf-util > babel-eslint > babel-traverse > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-hf-util > @babel/preset-es2015 > @babel/plugin-transform-modules-umd > @babel/helper-module-transforms > @babel/helper-simple-access > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-hf-util > @babel/preset-es2015 > @babel/plugin-transform-unicode-regex > @babel/helper-regex > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-hf-util > babel-eslint > babel-traverse > babel-types > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-hf-util > @babel/preset-es2015 > @babel/plugin-transform-parameters > @babel/helper-call-delegate > @babel/traverse > @babel/generator > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-hf-util > @babel/preset-es2015 > @babel/plugin-transform-modules-umd > @babel/helper-module-transforms > @babel/helper-simple-access > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-hf-util > @babel/preset-es2015 > @babel/plugin-transform-parameters > @babel/helper-call-delegate > @babel/traverse > @babel/helper-function-name > @babel/template > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-hf-util > @babel/preset-es2015 > @babel/plugin-transform-parameters > @babel/helper-call-delegate > @babel/traverse > @babel/generator > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-hf-util > @babel/preset-es2015 > @babel/plugin-transform-parameters > @babel/helper-call-delegate > @babel/traverse > @babel/helper-function-name > @babel/template > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-hf-util > @babel/preset-es2015 > @babel/plugin-transform-parameters > @babel/helper-call-delegate > @babel/traverse > @babel/helper-function-name > @babel/template > @babel/types > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-hf-util > @babel/preset-es2015 > @babel/plugin-transform-parameters > @babel/helper-call-delegate > @babel/traverse > @babel/helper-function-name > @babel/template > @babel/types > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-models > bfx-hf-util > @babel/preset-env > @babel/plugin-transform-exponentiation-operator > @babel/helper-builder-binary-assignment-operator-visitor > @babel/helper-explode-assignable-expression > @babel/traverse > @babel/helper-function-name > @babel/template > @babel/types > lodash:
        patched: '2020-07-06T09:32:09.747Z'
    - bitfinex-api-node > bfx-api-node-rest > bfx-api-node-models > bfx-hf-util > @babel/preset-env > @babel/plugin-transform-exponentiation-operator > @babel/helper-builder-binary-assignment-operator-visitor > @babel/helper-explode-assignable-expression > @babel/traverse > @babel/helper-function-name > @babel/template > @babel/types > lodash:
        patched: '2020-07-06T09:32:09.747Z'
