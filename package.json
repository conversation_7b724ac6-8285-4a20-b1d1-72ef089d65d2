{"name": "zenbot4", "version": "4.1.0", "description": "Cryptocurrency trading bot", "bugs": "https://github.com/deviavir/zenbot/issues", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/deviavir/zenbot.git"}, "main": "./index.js", "bin": {"zenbot": "./zenbot.sh"}, "scripts": {"lint": "eslint **/*.js", "lint-fix": "eslint **/*.js --fix", "precommit": "lint-staged", "test": "jasmine test/**/**.test.js", "test-one": "jasmine $PATH_TO_TEST", "postinstall": "node post_install.js", "snyk-protect": "snyk protect", "prepare": "npm run snyk-protect"}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "dependencies": {"@babel/core": "^7.17.2", "@slack/client": "^5.0.2", "adamant-api": "^1.3.0", "async": "^3.2.3", "babel-loader": "^8.2.3", "babel-preset-env": "^1.6.1", "bitfinex-api-node": "^4.0.16", "bitstamp": "^1.0.4", "bl": "^4.0.3", "bollinger-bands": "^3.0.2", "bootstrap": "^4.6.0", "ccxt": "^4.4.98", "cexio-api-node": "^1.0.8", "cliff": "^0.1.10", "coinbase-pro": "^0.9.0", "colors": "^1.4.0", "commander": "^5.1.0", "convnetjs": "0.3.0", "counterup": "^1.0.2", "css-loader": "^5.2.6", "echarts": "^5.3.0", "ejs": "^3.1.6", "exports-loader": "^0.7.0", "expose-loader": "^1.0.3", "express": "^4.17.2", "express-rate-limit": "^5.5.1", "file-loader": "^6.2.0", "fs": "0.0.2", "gemini-api": "^2.0.4", "glob": "^7.2.0", "har-validator": "^5.1.5", "husky": "^5.2.0", "idgen": "^2.0.2", "imports-loader": "^0.8.0", "ip": "~1.1.5", "jasmine": "^3.99.0", "jquery": "^3.6.0", "jsonexport": "^2.5.2", "keltnerchannel": "^1.4.2", "kraken-api": "^1.0.1", "lint-staged": "^10.5.3", "lodash": "^4.17.21", "lolex": "^6.0.0", "mathjs": "^7.6.0", "micro-request": "^666.0.10", "mime": "^2.6.0", "minimist": "^1.2.5", "mini-css-extract-plugin": "^1.6.2", "mock-require": "^3.0.2", "moment": "^2.29.1", "mongodb": "^3.7.3", "node-bittrex-api": "^0.8.2", "node-prowl": "^0.1.8", "node-sass": "^7.0.1", "node-telegram-bot-api": "^0.56.0", "number-abbreviate": "^2.0.0", "numbro": "github:highvelocityspace/numbro", "path": "^0.12.7", "poloniex.js": "1.0.1", "popper.js": "^1.16.1", "postcss-loader": "^4.3.0", "progress": "^2.0.0", "pushbullet": "2.4.0", "pusher-js": "^5.1.1", "random-port": "^0.1.0", "regression": "^2.0.0", "resolve-url-loader": "^4.0.0", "sass-loader": "^8.0.2", "semver": "^7.3.4", "simple-xmpp": "^1.3.0", "stats-lite": "2.2.0", "style-loader": "^1.3.0", "superagent": "^5.3.1", "talib": "^1.1.4", "timebucket": "^0.4.0", "trend": "0.3.0", "tulind": "^0.8.20", "url-loader": "^4.1.1", "waypoints": "^4.0.1", "webpack": "^5.68.0", "webpack-cli": "^4.9.2", "ws": "^7.5.7", "zero-fill": "^2.2.4", "snyk": "^1.842.0"}, "devDependencies": {"eslint": "^4.19.1", "geneticalgorithm": "^1.0.1", "json2csv": "^5.0.0", "round-precision": "^1.0.0", "run-parallel-limit": "^1.0.4", "shelljs": "^0.8.5", "yargs": "^16.1.1"}, "optionalDependencies": {"fsevents": "^2.1.2"}, "engines": {"node": ">=10.0.0"}, "snyk": true}